uuid: d45a05f3-d8eb-472a-8ff8-2a449e4d0720
langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.media.listing
    - media.type.remote_video
    - taxonomy.vocabulary.video_category
  module:
    - better_exposed_filters
    - media
    - taxonomy
    - user
    - views_load_more
id: videos
label: Videos
module: views
description: ''
tag: ''
base_table: media_field_data
base_field: mid
display:
  default:
    id: default
    display_title: Default
    display_plugin: default
    position: 0
    display_options:
      title: Videos
      fields:
        name:
          id: name
          table: media_field_data
          field: name
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: media
          entity_field: media
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            make_link: false
            absolute: false
            word_boundary: false
            ellipsis: false
            strip_tags: false
            trim: false
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
      pager:
        type: load_more
        options:
          offset: 0
          pagination_heading_level: h4
          items_per_page: 9
          total_pages: null
          id: 0
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
          more_button_text: 'See more'
          end_text: ''
          advanced:
            content_selector: ''
            pager_selector: ''
          effects:
            type: ''
            speed: slow
      exposed_form:
        type: bef
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
          text_input_required: 'Select any filter and click on Apply to see results'
          text_input_required_format: basic_html
          bef:
            general:
              autosubmit: true
              autosubmit_exclude_textfield: false
              autosubmit_textfield_delay: 500
              autosubmit_textfield_minimum_length: 3
              autosubmit_hide: true
              input_required: false
              allow_secondary: false
              secondary_label: 'Advanced options'
              secondary_open: false
              reset_button_always_show: false
            filter:
              field_category_target_id:
                plugin_id: bef_links
                advanced:
                  sort_options: false
                  rewrite:
                    filter_rewrite_values: '- Any -|All'
                    filter_rewrite_values_key: false
                  collapsible: false
                  collapsible_disable_automatic_open: false
                  is_secondary: false
                  hide_label: false
                select_all_none: false
      access:
        type: perm
        options:
          perm: 'view media'
      cache:
        type: tag
        options: {  }
      empty: {  }
      sorts:
        created:
          id: created
          table: media_field_data
          field: created
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: media
          entity_field: created
          plugin_id: date
          order: DESC
          expose:
            label: 'Authored on'
            field_identifier: created
          exposed: false
          granularity: second
      arguments: {  }
      filters:
        status:
          id: status
          table: media_field_data
          field: status
          entity_type: media
          entity_field: status
          plugin_id: boolean
          value: '1'
          group: 1
          expose:
            operator: ''
        bundle:
          id: bundle
          table: media_field_data
          field: bundle
          entity_type: media
          entity_field: bundle
          plugin_id: bundle
          value:
            remote_video: remote_video
        field_category_target_id:
          id: field_category_target_id
          table: media__field_category
          field: field_category_target_id
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: taxonomy_index_tid
          operator: or
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: field_category_target_id_op
            label: ''
            description: ''
            use_operator: false
            operator: field_category_target_id_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: category
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              talent: '0'
              company_admin: '0'
              company_recruiter: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
          vid: video_category
          type: select
          hierarchy: false
          limit: true
          error_message: true
      style:
        type: default
      row:
        type: 'entity:media'
        options:
          relationship: none
          view_mode: listing
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships: {  }
      use_ajax: true
      header: {  }
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - user.permissions
      tags: {  }
  page_1:
    id: page_1
    display_title: Page
    display_plugin: page
    position: 1
    display_options:
      display_extenders: {  }
      path: videos
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - user.permissions
      tags: {  }
