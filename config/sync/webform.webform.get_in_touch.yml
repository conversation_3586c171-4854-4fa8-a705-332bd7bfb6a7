uuid: 8366c369-8df3-4bd5-9de1-258b4b5d53e2
langcode: bg
status: open
dependencies: {  }
weight: 0
open: null
close: null
uid: 1
template: false
archive: false
id: get_in_touch
title: 'Свържи се с нас'
description: ''
categories: {  }
elements: |-
  container:
    '#type': container
    container_01:
      '#type': container
      container_05:
        '#type': container
        title:
          '#type': processed_text
          '#text': '<h2>Свържи се с нас</h2>'
          '#format': advanced_html
        full_name:
          '#type': textfield
          '#title': 'Име и Фамилия'
          '#placeholder': 'Въведете име и фамилия'
          '#required': true
        email:
          '#type': email
          '#title': Имейл
          '#placeholder': 'Въведете имейл'
          '#required': true
        type:
          '#type': radios
          '#title': Относно
          '#options':
            booth: 'Заяви щанд на събитие'
            partnership: 'Стани партньор'
            personal: 'Лични каузи'
          '#required': true
        message:
          '#type': textarea
          '#title': Съобщение
          '#placeholder': 'Кратко съобщение...'
          '#required': true
        marketing_consent:
          '#type': checkbox
          '#title': 'Желая да получавам маркетинг сьобщения и информация за нови сьбития и предложения, kakmo и информационен бюлетин на посочените по-горе комуникационни канали.'
        tnc_consent:
          '#type': checkbox
          '#title': 'Запознат/а сьм с <a href="/test">Политика за поверителност</a> и се съгласявам с Общите условия за начините на обработване на данните ми.'
          '#required': true
        actions:
          '#type': webform_actions
          '#title': 'Submit button(s)'
          '#submit__label': Изпрати
      container_06:
        '#type': container
        image:
          '#type': processed_text
          '#text': '<drupal-media data-entity-type="media" data-entity-uuid="7b954245-f6bc-4cd0-b2b3-c84ce093a629">&nbsp;</drupal-media>'
          '#format': advanced_html
    container_02:
      '#type': container
      container_03:
        '#type': container
        contacts:
          '#type': processed_text
          '#text': '<h3>Контакти</h3><ul><li>гр. София 1164 ул. "Галичица" Nº 22</li><li><EMAIL></li><li>+************</li><li>Понеделник - Петък от 9:00 gо 17:00ч.</li></ul>'
          '#format': advanced_html
        social:
          '#type': processed_text
          '#text': '<h3>Социални канали</h3><ul><li>FB</li><li>Instagram</li><li>LinkedIn</li><li>YouTube</li><li>Viber</li><li>TikTok</li></ul>'
          '#format': advanced_html
      container_04:
        '#type': container
css: ''
javascript: ''
settings:
  ajax: false
  ajax_scroll_top: form
  ajax_progress_type: ''
  ajax_effect: ''
  ajax_speed: null
  page: true
  page_submit_path: ''
  page_confirm_path: ''
  page_theme_name: ''
  form_title: both
  form_submit_once: false
  form_open_message: ''
  form_close_message: ''
  form_exception_message: ''
  form_previous_submissions: true
  form_confidential: false
  form_confidential_message: ''
  form_disable_remote_addr: false
  form_convert_anonymous: false
  form_prepopulate: false
  form_prepopulate_source_entity: false
  form_prepopulate_source_entity_required: false
  form_prepopulate_source_entity_type: ''
  form_unsaved: false
  form_disable_back: false
  form_submit_back: false
  form_disable_autocomplete: false
  form_novalidate: false
  form_disable_inline_errors: false
  form_required: false
  form_autofocus: false
  form_details_toggle: false
  form_reset: false
  form_access_denied: default
  form_access_denied_title: ''
  form_access_denied_message: ''
  form_access_denied_attributes: {  }
  form_file_limit: ''
  form_attributes: {  }
  form_method: ''
  form_action: ''
  share: false
  share_node: false
  share_theme_name: ''
  share_title: true
  share_page_body_attributes: {  }
  submission_label: ''
  submission_exception_message: ''
  submission_locked_message: ''
  submission_log: false
  submission_excluded_elements: {  }
  submission_exclude_empty: false
  submission_exclude_empty_checkbox: false
  submission_views: {  }
  submission_views_replace: {  }
  submission_user_columns: {  }
  submission_user_duplicate: false
  submission_access_denied: default
  submission_access_denied_title: ''
  submission_access_denied_message: ''
  submission_access_denied_attributes: {  }
  previous_submission_message: ''
  previous_submissions_message: ''
  autofill: false
  autofill_message: ''
  autofill_excluded_elements: {  }
  wizard_progress_bar: true
  wizard_progress_pages: false
  wizard_progress_percentage: false
  wizard_progress_link: false
  wizard_progress_states: false
  wizard_start_label: ''
  wizard_preview_link: false
  wizard_confirmation: true
  wizard_confirmation_label: ''
  wizard_auto_forward: true
  wizard_auto_forward_hide_next_button: false
  wizard_keyboard: true
  wizard_track: ''
  wizard_prev_button_label: ''
  wizard_next_button_label: ''
  wizard_toggle: false
  wizard_toggle_show_label: ''
  wizard_toggle_hide_label: ''
  wizard_page_type: container
  wizard_page_title_tag: h2
  preview: 0
  preview_label: ''
  preview_title: ''
  preview_message: ''
  preview_attributes: {  }
  preview_excluded_elements: {  }
  preview_exclude_empty: true
  preview_exclude_empty_checkbox: false
  draft: none
  draft_multiple: false
  draft_auto_save: false
  draft_saved_message: ''
  draft_loaded_message: ''
  draft_pending_single_message: ''
  draft_pending_multiple_message: ''
  confirmation_type: page
  confirmation_url: ''
  confirmation_title: ''
  confirmation_message: ''
  confirmation_attributes: {  }
  confirmation_back: true
  confirmation_back_label: ''
  confirmation_back_attributes: {  }
  confirmation_exclude_query: false
  confirmation_exclude_token: false
  confirmation_update: false
  limit_total: null
  limit_total_interval: null
  limit_total_message: ''
  limit_total_unique: false
  limit_user: null
  limit_user_interval: null
  limit_user_message: ''
  limit_user_unique: false
  entity_limit_total: null
  entity_limit_total_interval: null
  entity_limit_user: null
  entity_limit_user_interval: null
  purge: none
  purge_days: null
  results_disabled: false
  results_disabled_ignore: false
  results_customize: false
  token_view: false
  token_update: false
  token_delete: false
  serial_disabled: false
access:
  create:
    roles:
      - anonymous
      - authenticated
    users: {  }
    permissions: {  }
  view_any:
    roles: {  }
    users: {  }
    permissions: {  }
  update_any:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_any:
    roles: {  }
    users: {  }
    permissions: {  }
  purge_any:
    roles: {  }
    users: {  }
    permissions: {  }
  view_own:
    roles: {  }
    users: {  }
    permissions: {  }
  update_own:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_own:
    roles: {  }
    users: {  }
    permissions: {  }
  administer:
    roles: {  }
    users: {  }
    permissions: {  }
  test:
    roles: {  }
    users: {  }
    permissions: {  }
  configuration:
    roles: {  }
    users: {  }
    permissions: {  }
handlers: {  }
variants: {  }
