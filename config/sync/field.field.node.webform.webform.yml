uuid: e247b298-86ce-47df-9036-2c090c6a95f5
langcode: bg
status: true
dependencies:
  config:
    - field.storage.node.webform
    - node.type.webform
  module:
    - webform
_core:
  default_config_hash: w6hvTj8CaO8YVQDnlsD-jRBzMA_vNmx2cXLEKX13yT4
id: node.webform.webform
field_name: webform
entity_type: node
bundle: webform
label: Webform
description: 'Select the webform that you would like to attach to this node.'
required: false
translatable: false
default_value:
  -
    target_uuid: ''
    default_data: ''
    status: open
    open: ''
    close: ''
default_value_callback: ''
settings:
  handler: 'default:webform'
  handler_settings: {  }
field_type: webform
