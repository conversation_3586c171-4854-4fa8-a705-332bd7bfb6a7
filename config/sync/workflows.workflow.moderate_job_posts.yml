uuid: e4f9c504-04d5-4736-ac1b-ba3da06fbddb
langcode: en
status: true
dependencies:
  config:
    - node.type.job_post
  module:
    - content_moderation
id: moderate_job_posts
label: 'Moderate job posts'
type: content_moderation
type_settings:
  states:
    archived:
      label: Archived
      weight: 2
      published: false
      default_revision: true
    draft:
      label: Draft
      weight: -3
      published: false
      default_revision: false
    expired:
      label: Expired
      weight: 0
      published: false
      default_revision: true
    for_review:
      label: 'For review'
      weight: -2
      published: false
      default_revision: false
    published:
      label: Published
      weight: -1
      published: true
      default_revision: true
    unpublished:
      label: Unpublished
      weight: 1
      published: false
      default_revision: true
  transitions:
    archive:
      label: Archive
      from:
        - expired
        - published
        - unpublished
      to: archived
      weight: 3
    create_new_draft:
      label: 'Create New Draft'
      from:
        - archived
        - draft
        - expired
        - published
        - unpublished
      to: draft
      weight: 0
    expire:
      label: Expire
      from:
        - published
      to: expired
      weight: 5
    publish:
      label: Publish
      from:
        - archived
        - draft
        - expired
        - published
        - unpublished
      to: published
      weight: 1
    send_for_review:
      label: 'Send for review'
      from:
        - archived
        - draft
        - expired
        - unpublished
      to: for_review
      weight: 2
    unpublish:
      label: Unpublish
      from:
        - published
      to: unpublished
      weight: 4
  entity_types:
    node:
      - job_post
  default_moderation_state: draft
