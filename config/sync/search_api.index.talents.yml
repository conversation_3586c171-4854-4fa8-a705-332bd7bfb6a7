uuid: 0c9f0fcc-33e1-4b55-94b0-6f9aebf4bc8a
langcode: en
status: true
dependencies:
  config:
    - field.storage.profile.field_cv
    - field.storage.profile.field_education
    - field.storage.profile.field_first_name
    - field.storage.profile.field_interested_city
    - field.storage.profile.field_languages
    - field.storage.profile.field_last_name
    - field.storage.profile.field_linkedin
    - field.storage.profile.field_phone
    - field.storage.profile.field_photo
    - field.storage.profile.field_professional_field
    - field.storage.profile.field_residence_place
    - field.storage.profile.field_talent_profile_consent
    - search_api.server.bwy_solr_server
  module:
    - profile
    - search_api_attachments
    - search_api_solr
    - taxonomy
third_party_settings:
  search_api_solr:
    finalize: false
    commit_before_finalize: false
    commit_after_finalize: false
    debug_finalize: false
    highlighter:
      maxAnalyzedChars: 51200
      fragmenter: gap
      usePhraseHighlighter: true
      highlightMultiTerm: true
      preserveMulti: false
      regex:
        slop: 0.5
        pattern: blank
        maxAnalyzedChars: 10000
      highlight:
        mergeContiguous: false
        requireFieldMatch: false
        snippets: 3
        fragsize: 0
    mlt:
      mintf: 1
      mindf: 1
      maxdf: 0
      maxdfpct: 0
      minwl: 0
      maxwl: 0
      maxqt: 100
      maxntp: 2000
      boost: false
      interestingTerms: none
    term_modifiers:
      slop: 3
      fuzzy: 1
      fuzzy_analyzer: true
    advanced:
      index_prefix: ''
      collection: ''
      timezone: ''
    multilingual:
      limit_to_content_language: false
      include_language_independent: true
      use_language_undefined_as_fallback_language: false
      specific_languages:
        en: '0'
        bg: '0'
      use_universal_collation: false
id: talents
name: Talents
description: ''
read_only: false
field_settings:
  city:
    label: 'City » Taxonomy term » Name'
    datasource_id: 'entity:profile'
    property_path: 'field_interested_city:entity:name'
    type: text
    dependencies:
      config:
        - field.storage.profile.field_interested_city
      module:
        - taxonomy
  city_tid:
    label: 'City » Taxonomy term » Term ID'
    datasource_id: 'entity:profile'
    property_path: 'field_interested_city:entity:tid'
    type: integer
    dependencies:
      config:
        - field.storage.profile.field_interested_city
      module:
        - taxonomy
  cv:
    label: CV
    datasource_id: 'entity:profile'
    property_path: field_cv
    type: integer
    dependencies:
      config:
        - field.storage.profile.field_cv
  education:
    label: 'Education » Taxonomy term » Name'
    datasource_id: 'entity:profile'
    property_path: 'field_education:entity:name'
    type: text
    dependencies:
      config:
        - field.storage.profile.field_education
      module:
        - taxonomy
  education_tid:
    label: 'Education » Taxonomy term » Term ID'
    datasource_id: 'entity:profile'
    property_path: 'field_education:entity:tid'
    type: integer
    dependencies:
      config:
        - field.storage.profile.field_education
      module:
        - taxonomy
  first_name:
    label: 'First name'
    datasource_id: 'entity:profile'
    property_path: field_first_name
    type: text
    dependencies:
      config:
        - field.storage.profile.field_first_name
  languages:
    label: 'Languages » Taxonomy term » Name'
    datasource_id: 'entity:profile'
    property_path: 'field_languages:entity:name'
    type: text
    dependencies:
      config:
        - field.storage.profile.field_languages
      module:
        - taxonomy
  languages_tid:
    label: 'Languages » Taxonomy term » Term ID'
    datasource_id: 'entity:profile'
    property_path: 'field_languages:entity:tid'
    type: integer
    dependencies:
      config:
        - field.storage.profile.field_languages
      module:
        - taxonomy
  last_name:
    label: 'Last name'
    datasource_id: 'entity:profile'
    property_path: field_last_name
    type: text
    dependencies:
      config:
        - field.storage.profile.field_last_name
  linkedin:
    label: 'LinkedIn profile'
    datasource_id: 'entity:profile'
    property_path: field_linkedin
    type: string
    dependencies:
      config:
        - field.storage.profile.field_linkedin
  phone:
    label: 'Phone number'
    datasource_id: 'entity:profile'
    property_path: field_phone
    type: string
    dependencies:
      config:
        - field.storage.profile.field_phone
  photo:
    label: Photo
    datasource_id: 'entity:profile'
    property_path: field_photo
    type: integer
    dependencies:
      config:
        - field.storage.profile.field_photo
  professional_field:
    label: 'Professional field » Taxonomy term » Name'
    datasource_id: 'entity:profile'
    property_path: 'field_professional_field:entity:name'
    type: text
    dependencies:
      config:
        - field.storage.profile.field_professional_field
      module:
        - taxonomy
  professional_field_tid:
    label: 'Professional field » Taxonomy term » Term ID'
    datasource_id: 'entity:profile'
    property_path: 'field_professional_field:entity:tid'
    type: integer
    dependencies:
      config:
        - field.storage.profile.field_professional_field
      module:
        - taxonomy
  profile_consent:
    label: 'The profile is accessible to employers'
    datasource_id: 'entity:profile'
    property_path: field_talent_profile_consent
    type: boolean
    dependencies:
      config:
        - field.storage.profile.field_talent_profile_consent
  profile_type:
    label: 'Profile type'
    datasource_id: 'entity:profile'
    property_path: type
    type: string
    dependencies:
      module:
        - profile
  rendered_item:
    label: 'Rendered HTML output'
    property_path: rendered_item
    type: text
    configuration:
      roles:
        - anonymous
      view_mode: {  }
  residence_place:
    label: 'Residence place'
    datasource_id: 'entity:profile'
    property_path: field_residence_place
    type: text
    dependencies:
      config:
        - field.storage.profile.field_residence_place
  saa_field_certificates:
    label: 'Search api attachments: Certificates'
    property_path: saa_field_certificates
    type: text
    boost: 1.2
  saa_field_cv:
    label: 'Search api attachments: CV'
    property_path: saa_field_cv
    type: text
    boost: 2.0
datasource_settings:
  'entity:profile':
    bundles:
      default: true
      selected: {  }
processor_settings:
  add_url: {  }
  aggregated_field: {  }
  auto_aggregated_fulltext_field: {  }
  custom_value: {  }
  entity_status: {  }
  entity_type: {  }
  file_attachments:
    excluded_extensions: 'aif art avi bmp gif ico mov oga ogv png psd ra ram rgb flv'
    number_indexed: 0
    number_first_bytes: '1 MB'
    max_filesize: '0'
    excluded_private: 0
    excluded_mimes: 'audio/x-aiff image/x-jg video/x-msvideo image/x-ms-bmp image/gif image/vnd.microsoft.icon video/quicktime audio/ogg video/ogg image/png image/x-photoshop audio/x-realaudio audio/x-pn-realaudio image/x-rgb video/x-flv'
  language_with_fallback: {  }
  rendered_item: {  }
  solr_date_range:
    weights:
      preprocess_index: 0
tracker_settings:
  default:
    indexing_order: fifo
options:
  cron_limit: 50
  delete_on_fail: true
  index_directly: true
  track_changes_in_references: true
server: bwy_solr_server
