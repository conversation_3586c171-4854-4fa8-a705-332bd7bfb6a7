uuid: e7db0a68-4a73-4fb1-a037-0bc08082efc3
langcode: bg
status: true
dependencies:
  module:
    - options
    - paragraphs
id: paragraph.field_markup_type
field_name: field_markup_type
entity_type: paragraph
type: list_string
settings:
  allowed_values:
    -
      value: become_member_v1
      label: 'Стани наш партньор v1'
    -
      value: become_member_v2
      label: 'Стани наш партньор v2'
  allowed_values_function: ''
module: options
locked: false
cardinality: 1
translatable: true
indexes: {  }
persist_with_no_fields: false
custom_storage: false
