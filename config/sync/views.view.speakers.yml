uuid: 5ecdf970-934b-480c-bd0b-476b1ab1cb5e
langcode: bg
status: true
dependencies:
  config:
    - core.entity_view_mode.node.speaker_card
    - node.type.event
  module:
    - node
    - paragraphs
    - user
id: speakers
label: Speakers
module: views
description: ''
tag: ''
base_table: node_field_data
base_field: nid
display:
  default:
    id: default
    display_title: Default
    display_plugin: default
    position: 0
    display_options:
      title: Speakers
      fields:
        title:
          id: title
          table: node_field_data
          field: title
          relationship: field_nodes
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
      pager:
        type: none
        options:
          offset: 0
          items_per_page: null
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      access:
        type: perm
        options:
          perm: 'access content'
      cache:
        type: tag
        options: {  }
      empty: {  }
      sorts: {  }
      arguments:
        nid:
          id: nid
          table: node_field_data
          field: nid
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: nid
          plugin_id: node_nid
          default_action: default
          exception:
            value: all
            title_enable: false
            title: All
          title_enable: false
          title: ''
          default_argument_type: node
          default_argument_options: {  }
          summary_options:
            base_path: ''
            count: true
            override: false
            items_per_page: 25
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: false
          validate:
            type: none
            fail: 'not found'
          validate_options: {  }
          break_phrase: false
          not: false
      filters:
        status:
          id: status
          table: node_field_data
          field: status
          entity_type: node
          entity_field: status
          plugin_id: boolean
          value: '1'
          group: 1
          expose:
            operator: ''
        type:
          id: type
          table: node_field_data
          field: type
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: type
          plugin_id: bundle
          operator: in
          value:
            event: event
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      style:
        type: default
      row:
        type: 'entity:node'
        options:
          relationship: field_nodes
          view_mode: speaker_card
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships:
        field_schedule:
          id: field_schedule
          table: node__field_schedule
          field: field_schedule
          relationship: none
          group_type: group
          admin_label: 'field_schedule: Paragraph'
          plugin_id: standard
          required: true
        field_schedule_slot:
          id: field_schedule_slot
          table: paragraph__field_schedule_slot
          field: field_schedule_slot
          relationship: field_schedule
          group_type: group
          admin_label: 'field_schedule_slot: Paragraph'
          plugin_id: standard
          required: true
        field_nodes:
          id: field_nodes
          table: paragraph__field_nodes
          field: field_nodes
          relationship: field_schedule_slot
          group_type: group
          admin_label: speakers
          plugin_id: standard
          required: true
      header: {  }
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
  speakers:
    id: speakers
    display_title: Speakers
    display_plugin: block
    position: 1
    display_options:
      display_description: ''
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
