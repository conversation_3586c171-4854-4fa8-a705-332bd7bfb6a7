uuid: ec9cc2a1-0f1f-423e-b701-4be34d066179
langcode: en
status: true
dependencies:
  config:
    - field.storage.profile.field_photo
    - profile.type.talent
  module:
    - image
id: profile.talent.field_photo
field_name: field_photo
entity_type: profile
bundle: talent
label: Photo
description: 'Upload your up to date profile photo'
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:file'
  handler_settings: {  }
  file_directory: talent-photos
  file_extensions: 'png gif jpg jpeg webp'
  max_filesize: 10MB
  max_resolution: ''
  min_resolution: 400x400
  alt_field: false
  alt_field_required: true
  title_field: false
  title_field_required: false
  default_image:
    uuid: ''
    alt: ''
    title: ''
    width: null
    height: null
field_type: image
