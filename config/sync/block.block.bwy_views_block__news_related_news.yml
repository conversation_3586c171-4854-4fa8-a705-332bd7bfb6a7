uuid: c31a1358-47b7-4a7b-a90b-ddbb081f48f9
langcode: en
status: true
dependencies:
  config:
    - views.view.news
  module:
    - node
    - views
  theme:
    - bwy
id: bwy_views_block__news_related_news
theme: bwy
region: content
weight: -2
provider: null
plugin: 'views_block:news-related_news'
settings:
  id: 'views_block:news-related_news'
  label: ''
  label_display: visible
  provider: views
  context_mapping: {  }
  views_label: ''
  items_per_page: none
visibility:
  'entity_bundle:node':
    id: 'entity_bundle:node'
    negate: false
    context_mapping:
      node: '@node.node_route_context:node'
    bundles:
      news: news
