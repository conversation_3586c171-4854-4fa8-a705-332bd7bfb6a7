uuid: 3cc42137-61f7-4a4a-ac85-eca61ddae2a3
langcode: bg
status: true
dependencies:
  config:
    - core.entity_view_mode.media.full
  module:
    - media
name: 'Advanced HTML'
format: advanced_html
weight: 0
filters:
  filter_align:
    id: filter_align
    provider: filter
    status: true
    weight: 0
    settings: {  }
  filter_caption:
    id: filter_caption
    provider: filter
    status: true
    weight: 0
    settings: {  }
  filter_html:
    id: filter_html
    provider: filter
    status: true
    weight: -10
    settings:
      allowed_html: '<br> <p class="text-align-left text-align-center text-align-right text-align-justify"> <h2 class="text-align-left text-align-center text-align-right text-align-justify"> <h3 class="text-align-left text-align-center text-align-right text-align-justify"> <h4 class="text-align-left text-align-center text-align-right text-align-justify"> <h5 class="text-align-left text-align-center text-align-right text-align-justify"> <h6 class="text-align-left text-align-center text-align-right text-align-justify"> <strong> <em> <u> <sub> <sup> <blockquote> <a href> <ul> <ol start> <li> <drupal-media data-entity-type data-entity-uuid alt data-caption data-align>'
      filter_html_help: true
      filter_html_nofollow: false
  filter_html_image_secure:
    id: filter_html_image_secure
    provider: filter
    status: true
    weight: 9
    settings: {  }
  filter_image_lazy_load:
    id: filter_image_lazy_load
    provider: filter
    status: true
    weight: 15
    settings: {  }
  media_embed:
    id: media_embed
    provider: media
    status: true
    weight: 100
    settings:
      default_view_mode: default
      allowed_view_modes:
        full: full
      allowed_media_types:
        image: image
        remote_video: remote_video
