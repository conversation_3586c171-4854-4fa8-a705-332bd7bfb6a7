uuid: 67e04d8a-d012-49db-ab18-09339ac9b8d0
langcode: bg
status: true
dependencies:
  config:
    - field.field.node.landing_page.field_components
    - field.field.node.landing_page.field_hero_button
    - field.field.node.landing_page.field_hero_subtitle
    - field.field.node.landing_page.field_hero_theme
    - field.field.node.landing_page.field_metatags
    - node.type.landing_page
  module:
    - content_moderation
    - field_group
    - link
    - metatag
    - paragraphs
    - path
third_party_settings:
  field_group:
    group_hero_section:
      children:
        - field_hero_subtitle
        - field_hero_button
        - field_hero_theme
      label: 'Hero section'
      region: content
      parent_name: ''
      weight: 1
      format_type: details
      format_settings:
        classes: ''
        show_empty_fields: false
        id: ''
        label_as_html: false
        open: false
        description: ''
        required_fields: true
id: node.landing_page.default
targetEntityType: node
bundle: landing_page
mode: default
content:
  created:
    type: datetime_timestamp
    weight: 4
    region: content
    settings: {  }
    third_party_settings: {  }
  field_components:
    type: paragraphs
    weight: 14
    region: content
    settings:
      title: Paragraph
      title_plural: Paragraphs
      edit_mode: open
      closed_mode: summary
      autocollapse: none
      closed_mode_threshold: 0
      add_mode: dropdown
      form_display_mode: default
      default_paragraph_type: ''
      features:
        collapse_edit_all: collapse_edit_all
        duplicate: duplicate
    third_party_settings: {  }
  field_hero_button:
    type: link_default
    weight: 17
    region: content
    settings:
      placeholder_url: ''
      placeholder_title: ''
    third_party_settings: {  }
  field_hero_subtitle:
    type: string_textfield
    weight: 16
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_hero_theme:
    type: options_select
    weight: 18
    region: content
    settings: {  }
    third_party_settings: {  }
  field_metatags:
    type: metatag_firehose
    weight: 13
    region: content
    settings:
      sidebar: true
      use_details: true
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 2
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  moderation_state:
    type: moderation_state_default
    weight: 11
    region: content
    settings: {  }
    third_party_settings: {  }
  path:
    type: path
    weight: 9
    region: content
    settings: {  }
    third_party_settings: {  }
  promote:
    type: boolean_checkbox
    weight: 7
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  simple_sitemap:
    weight: 5
    region: content
    settings: {  }
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 12
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  sticky:
    type: boolean_checkbox
    weight: 8
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  translation:
    weight: 6
    region: content
    settings: {  }
    third_party_settings: {  }
  uid:
    type: entity_reference_autocomplete
    weight: 3
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  url_redirects:
    weight: 10
    region: content
    settings: {  }
    third_party_settings: {  }
hidden:
  entitygroupfield: true
  publish_on: true
  publish_state: true
  unpublish_on: true
  unpublish_state: true
