label: 'Group nodes'
description: 'Lists all of the nodes that have been added to a group.'
display:
  default:
    display_title: Master
    display_options:
      title: Nodes
      fields:
        title:
          label: Title
          separator: ', '
        type:
          label: 'Content type'
          separator: ', '
        status:
          label: Status
          settings:
            format_custom_false: Unpublished
            format_custom_true: Published
          separator: ', '
        changed:
          label: Updated
          separator: ', '
        view_group_relationship:
          admin_label: 'View relation link'
          label: 'Link to Group relationship'
          text: 'View relation'
        edit_group_relationship:
          admin_label: 'Edit relation link'
          label: 'Link to edit Group relationship'
          text: 'Edit relation'
        delete_group_relationship:
          admin_label: 'Delete relation link'
          label: 'Link to delete Group relationship'
          text: 'Delete relation'
        edit_node:
          admin_label: 'Edit node link'
          label: 'Link to edit Content'
          text: 'Edit node'
        delete_node:
          admin_label: 'Delete node link'
          label: 'Link to delete Content'
          text: 'Delete node'
        dropbutton:
          label: Operations
      pager:
        options:
          tags:
            next: ››
            previous: ‹‹
            first: '« First'
            last: 'Last »'
          expose:
            items_per_page_label: 'Items per page'
            items_per_page_options_all_label: '- All -'
            offset_label: Offset
      exposed_form:
        options:
          submit_button: Apply
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          sort_asc_label: Asc
          sort_desc_label: Desc
      empty:
        area_text_custom:
          content: 'No content available.'
      arguments:
        gid:
          exception:
            title: All
          title: '{{ arguments.gid|placeholder }} nodes'
      filters:
        status:
          expose:
            label: 'Published status'
          group_info:
            label: 'Published status'
            group_items:
              1:
                title: Published
              2:
                title: Unpublished
        type:
          expose:
            label: Type
  page_1:
    display_title: Page
    display_options:
      menu:
        title: Nodes
