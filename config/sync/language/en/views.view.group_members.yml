label: 'Group members'
display:
  default:
    display_title: Master
    display_options:
      title: Members
      fields:
        name:
          label: User
          separator: ', '
        group_roles:
          label: Roles
          separator: ', '
        changed:
          label: Updated
          separator: ', '
        created:
          label: Joined
          separator: ', '
        view_group_relationship:
          admin_label: 'View member link'
          text: 'View member'
        edit_group_relationship:
          admin_label: 'Edit member link'
          text: 'Edit member'
        delete_group_relationship:
          admin_label: 'Remove member link'
          text: 'Remove member'
        dropbutton:
          label: Operations
      pager:
        options:
          tags:
            next: ››
            previous: ‹‹
            first: '« First'
            last: 'Last »'
          expose:
            items_per_page_label: 'Items per page'
            items_per_page_options_all_label: '- All -'
            offset_label: Offset
      exposed_form:
        options:
          submit_button: Apply
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          sort_asc_label: Asc
          sort_desc_label: Desc
      empty:
        area_text_custom:
          content: 'No members available.'
      arguments:
        gid:
          exception:
            title: All
          title: '{{ arguments.gid|placeholder }} members'
  page_1:
    display_title: Page
    display_options:
      menu:
        title: Members
