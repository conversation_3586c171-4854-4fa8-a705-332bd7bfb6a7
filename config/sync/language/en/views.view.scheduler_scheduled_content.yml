label: 'Scheduled Content'
description: 'Find and manage scheduled content.'
display:
  default:
    display_options:
      exposed_form:
        options:
          submit_button: Filter
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          sort_asc_label: Asc
          sort_desc_label: Desc
      pager:
        options:
          tags:
            previous: '‹ previous'
            next: 'next ›'
            first: '« first'
            last: 'last »'
      fields:
        node_bulk_form:
          action_title: Action
        title:
          label: Title
          separator: ', '
        type:
          label: 'Content Type'
          separator: ', '
        name:
          label: Author
          separator: ', '
        status:
          label: Status
          settings:
            format_custom_true: Published
            format_custom_false: Unpublished
          separator: ', '
        publish_on:
          label: 'Publish on'
          separator: ', '
        unpublish_on:
          label: 'Unpublish on'
          separator: ', '
        operations:
          label: Operations
      filters:
        title:
          expose:
            label: Title
        type:
          expose:
            label: 'Content type'
        status:
          expose:
            label: Status
          group_info:
            label: 'Published status'
            group_items:
              1:
                title: Published
              2:
                title: Unpublished
        langcode:
          expose:
            label: Language
      title: 'Scheduled Content'
      empty:
        area_text_custom:
          content: 'No scheduled content.'
    display_title: Master
  overview:
    display_options:
      menu:
        title: 'Scheduled Content'
        description: 'Content that is scheduled for publishing or unpublishing'
      tab_options:
        title: Content
        description: 'Find and manage scheduled content'
      display_description: "Overview of all scheduled content, as a tab on main 'content admin' page"
      display_comment: "Revision nid relationship is required because the content type is only stored at 'content' level, not 'content revision' level."
    display_title: 'Content Overview'
  user_page:
    display_options:
      menu:
        title: Scheduled
      tab_options:
        title: Content
        description: 'Find and manage scheduled content'
      display_description: "Scheduled content tab on user profile, showing just that user's scheduled content"
      arguments:
        uid:
          exception:
            title: All
      empty:
        area_text_custom:
          content: 'No scheduled content for user {{ arguments.uid }}'
      display_comment: 'Access to the user view is controlled via SchedulerRouteSubscriber::alterRoutes(). The high-level permission "access content" is added to satisfy the security_review module'
    display_title: User
