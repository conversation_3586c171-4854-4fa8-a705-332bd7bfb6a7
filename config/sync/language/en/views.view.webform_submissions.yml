label: 'Webform submissions'
description: 'Default webform submissions views.'
display:
  default:
    display_title: Master
    display_options:
      exposed_form:
        options:
          submit_button: Apply
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          sort_asc_label: Asc
          sort_desc_label: Desc
      pager:
        options:
          tags:
            previous: ‹‹
            next: ››
            first: '« First'
            last: 'Last »'
          expose:
            items_per_page_label: 'Items per page'
            items_per_page_options_all_label: '- All -'
            offset_label: Offset
      fields:
        sid:
          label: '#'
          separator: ', '
        in_draft:
          label: Draft
          separator: ', '
        created:
          label: Created
          separator: ', '
        remote_addr:
          label: 'IP address'
          separator: ', '
        view_webform_submission:
          label: Operations
          text: view
      header:
        result:
          content: 'Displaying @start - @end of @total'
      empty:
        area_text_custom:
          content: 'No submissions available.'
      arguments:
        in_draft:
          exception:
            title: All
      title: 'Webform submissions'
  embed_default:
    display_title: 'Embed: Default'
    display_options:
      display_description: 'Display submissions.'
  embed_administer:
    display_title: 'Embed: Administer'
    display_options:
      display_description: 'Administer submissions.'
      fields:
        webform_submission_bulk_form:
          label: 'Webform submission operations bulk form'
          action_title: Action
        sid:
          label: '#'
          separator: ', '
        in_draft:
          label: Draft
          separator: ', '
        sticky:
          label: Sticky
          separator: ', '
        locked:
          label: Locked
          separator: ', '
        created:
          label: Created
          separator: ', '
        completed:
          label: Completed
          separator: ', '
        remote_addr:
          label: 'IP address'
          separator: ', '
        operations:
          label: Operations
      filters:
        in_draft:
          expose:
            label: 'Is draft'
        sticky:
          expose:
            label: Sticky
        locked:
          expose:
            label: Locked
  embed_manage:
    display_title: 'Embed: Manage'
    display_options:
      display_description: 'Manage submissions.'
      fields:
        webform_submission_bulk_form:
          label: 'Webform submission operations bulk form'
          action_title: Action
        sid:
          label: '#'
          separator: ', '
        in_draft:
          label: Draft
          separator: ', '
        sticky:
          label: Sticky
          separator: ', '
        locked:
          label: Locked
          separator: ', '
        created:
          label: Created
          separator: ', '
        completed:
          label: Completed
          separator: ', '
        remote_addr:
          label: 'IP address'
          separator: ', '
        view_webform_submission:
          label: Operations
          text: view
        edit_webform_submission:
          label: Operations
          text: edit
      filters:
        in_draft:
          expose:
            label: 'Is draft'
        sticky:
          expose:
            label: Sticky
        locked:
          expose:
            label: Locked
  embed_review:
    display_title: 'Embed: Review'
    display_options:
      display_description: 'Review submissions.'
      fields:
        sid:
          label: '#'
          separator: ', '
        in_draft:
          label: Draft
          separator: ', '
        sticky:
          label: Sticky
          separator: ', '
        locked:
          label: Locked
          separator: ', '
        created:
          label: Created
          separator: ', '
        completed:
          label: Completed
          separator: ', '
        remote_addr:
          label: 'IP address'
          separator: ', '
        view_webform_submission:
          label: Operations
          text: view
      filters:
        in_draft:
          expose:
            label: 'Is draft'
        sticky:
          expose:
            label: Sticky
        locked:
          expose:
            label: Locked
