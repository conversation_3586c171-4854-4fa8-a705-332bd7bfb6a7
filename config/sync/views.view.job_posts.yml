uuid: 34d95078-92f7-4496-a56b-56fe4d908d6d
langcode: en
status: true
dependencies:
  config:
    - field.storage.group.field_company_logo
    - field.storage.node.field_gross_net
    - field.storage.node.field_reference_number
    - field.storage.node.field_salary_from
    - field.storage.node.field_salary_to
    - image.style.thumbnail
    - search_api.index.job_posts
  module:
    - better_exposed_filters
    - facets_exposed_filters
    - image
    - options
    - search_api
    - user
    - views_load_more
id: job_posts
label: 'Job posts listing'
module: views
description: 'Job posts full listing page.'
tag: 'Job posts'
base_table: search_api_index_job_posts
base_field: search_api_id
display:
  default:
    id: default
    display_title: Default
    display_plugin: default
    position: 0
    display_options:
      title: ''
      fields:
        logo:
          id: logo
          table: search_api_index_job_posts
          field: logo
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: search_api_field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: image
          settings:
            image_link: ''
            image_style: thumbnail
            image_loading:
              attribute: lazy
          group_column: ''
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
          field_rendering: true
          fallback_handler: search_api
          fallback_options:
            link_to_item: false
            use_highlighting: false
            multi_type: separator
            multi_separator: ', '
        company_name:
          id: company_name
          table: search_api_index_job_posts
          field: company_name
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: search_api_field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
          field_rendering: false
          fallback_handler: search_api
          fallback_options:
            link_to_item: false
            use_highlighting: false
            multi_type: separator
            multi_separator: ', '
        field_reference_number:
          id: field_reference_number
          table: search_api_index_job_posts
          field: field_reference_number
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: search_api_field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: true
          field_rendering: true
          fallback_handler: search_api
          fallback_options:
            link_to_item: false
            use_highlighting: false
            multi_type: separator
            multi_separator: ', '
        title:
          id: title
          table: search_api_index_job_posts
          field: title
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: null
          entity_field: null
          plugin_id: search_api_field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
          field_rendering: false
          fallback_handler: search_api
          fallback_options:
            link_to_item: true
            use_highlighting: false
            multi_type: separator
            multi_separator: ', '
        professional_field:
          id: professional_field
          table: search_api_index_job_posts
          field: professional_field
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: search_api_field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
          field_rendering: false
          fallback_handler: search_api
          fallback_options:
            link_to_item: false
            use_highlighting: false
            multi_type: separator
            multi_separator: ', '
        place_of_work:
          id: place_of_work
          table: search_api_index_job_posts
          field: place_of_work
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: search_api_field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
          field_rendering: true
          fallback_handler: search_api
          fallback_options:
            link_to_item: false
            use_highlighting: false
            multi_type: separator
            multi_separator: ', '
        work_type:
          id: work_type
          table: search_api_index_job_posts
          field: work_type
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: search_api_field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
          field_rendering: true
          fallback_handler: search_api
          fallback_options:
            link_to_item: false
            use_highlighting: false
            multi_type: separator
            multi_separator: ', '
        field_salary_from:
          id: field_salary_from
          table: search_api_index_job_posts
          field: field_salary_from
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: search_api_field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_decimal
          settings:
            thousand_separator: "\t"
            decimal_separator: .
            scale: 0
            prefix_suffix: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
          field_rendering: true
          fallback_handler: search_api_numeric
          fallback_options:
            set_precision: false
            precision: 0
            decimal: .
            separator: ','
            format_plural: false
            format_plural_string: !!binary MQNAY291bnQ=
            prefix: ''
            suffix: ''
            link_to_item: false
            use_highlighting: false
            multi_type: separator
            multi_separator: ', '
            format_plural_values:
              - '1'
              - '@count'
        field_salary_to:
          id: field_salary_to
          table: search_api_index_job_posts
          field: field_salary_to
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: search_api_field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_decimal
          settings:
            thousand_separator: "\t"
            decimal_separator: .
            scale: 0
            prefix_suffix: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
          field_rendering: true
          fallback_handler: search_api_numeric
          fallback_options:
            set_precision: false
            precision: 0
            decimal: .
            separator: ','
            format_plural: false
            format_plural_string: !!binary MQNAY291bnQ=
            prefix: ''
            suffix: ''
            link_to_item: false
            use_highlighting: false
            multi_type: separator
            multi_separator: ', '
            format_plural_values:
              - '1'
              - '@count'
        salary_type:
          id: salary_type
          table: search_api_index_job_posts
          field: salary_type
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: search_api_field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: list_default
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
          field_rendering: true
          fallback_handler: search_api
          fallback_options:
            link_to_item: false
            use_highlighting: false
            multi_type: separator
            multi_separator: ', '
        created_1:
          id: created_1
          table: search_api_index_job_posts
          field: created
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: search_api_field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: timestamp
          settings:
            date_format: short_format
            custom_date_format: ''
            timezone: ''
            tooltip:
              date_format: short_format
              custom_date_format: ''
            time_diff:
              enabled: false
              future_format: '@interval hence'
              past_format: '@interval ago'
              granularity: 2
              refresh: 60
              description: ''
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
          field_rendering: true
          fallback_handler: search_api_date
          fallback_options:
            date_format: bwy_event_date
            custom_date_format: ''
            timezone: ''
            link_to_item: false
            use_highlighting: false
            multi_type: separator
            multi_separator: ', '
        unpublish_on_1:
          id: unpublish_on_1
          table: search_api_index_job_posts
          field: unpublish_on
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: search_api_field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: timestamp
          settings:
            date_format: short_format
            custom_date_format: ''
            timezone: ''
            tooltip:
              date_format: short_format
              custom_date_format: ''
            time_diff:
              enabled: false
              future_format: '@interval hence'
              past_format: '@interval ago'
              granularity: 2
              refresh: 60
              description: ''
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
          field_rendering: true
          fallback_handler: search_api
          fallback_options:
            link_to_item: false
            use_highlighting: false
            multi_type: separator
            multi_separator: ', '
      pager:
        type: full
        options:
          offset: 0
          pagination_heading_level: h4
          items_per_page: 20
          total_pages: null
          id: 0
          tags:
            next: ››
            previous: ‹‹
            first: '« First'
            last: 'Last »'
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
          quantity: 9
      exposed_form:
        type: bef
        options:
          submit_button: Apply
          reset_button: true
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
          text_input_required: 'Select any filter and click on Apply to see results'
          text_input_required_format: basic_html
          bef:
            general:
              autosubmit: true
              autosubmit_exclude_textfield: false
              autosubmit_textfield_delay: 500
              autosubmit_textfield_minimum_length: 3
              autosubmit_hide: true
              input_required: false
              allow_secondary: false
              secondary_label: 'Advanced options'
              secondary_open: false
              reset_button_always_show: false
            filter:
              facets_place_of_work:
                plugin_id: bef
                advanced:
                  rewrite:
                    filter_rewrite_values: ''
                    filter_rewrite_values_key: false
                  collapsible: false
                  collapsible_disable_automatic_open: false
                  is_secondary: false
                  hide_label: false
                select_all_none: false
                select_all_none_nested: false
                display_inline: true
              facets_professional_field:
                plugin_id: bef
                advanced:
                  rewrite:
                    filter_rewrite_values: ''
                    filter_rewrite_values_key: false
                  collapsible: false
                  collapsible_disable_automatic_open: false
                  is_secondary: false
                  hide_label: false
                select_all_none: false
                select_all_none_nested: false
                display_inline: true
              facets_work_type:
                plugin_id: bef
                advanced:
                  rewrite:
                    filter_rewrite_values: ''
                    filter_rewrite_values_key: false
                  collapsible: false
                  collapsible_disable_automatic_open: false
                  is_secondary: false
                  hide_label: false
                select_all_none: false
                select_all_none_nested: false
                display_inline: true
              facets_company_name:
                plugin_id: bef
                advanced:
                  rewrite:
                    filter_rewrite_values: ''
                    filter_rewrite_values_key: false
                  collapsible: false
                  collapsible_disable_automatic_open: false
                  is_secondary: false
                  hide_label: false
                select_all_none: false
                select_all_none_nested: false
                display_inline: true
              facets_language:
                plugin_id: bef
                advanced:
                  rewrite:
                    filter_rewrite_values: ''
                    filter_rewrite_values_key: false
                  collapsible: false
                  collapsible_disable_automatic_open: false
                  is_secondary: false
                  hide_label: false
                select_all_none: false
                select_all_none_nested: false
                display_inline: true
      access:
        type: perm
        options:
          perm: 'access content'
      cache:
        type: search_api_none
        options: {  }
      empty: {  }
      sorts:
        created:
          id: created
          table: search_api_index_job_posts
          field: created
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: search_api
          order: DESC
          expose:
            label: ''
            field_identifier: ''
          exposed: false
      arguments: {  }
      filters:
        status:
          id: status
          table: search_api_index_job_posts
          field: status
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: search_api_boolean
          operator: '='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        facets_place_of_work:
          id: facets_place_of_work
          table: search_api_index_job_posts
          field: facets_place_of_work
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: facets_filter
          operator: '='
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: ''
            label: 'Place of work'
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: place_of_work
            required: false
            remember: false
            multiple: true
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              talent: '0'
              company_admin: '0'
              company_recruiter: '0'
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          hierarchy: false
          label_display: visible
          facet:
            query_operator: or
            min_count: 1
            show_numbers: true
            processor_configs:
              hide_non_narrowing_result_processor:
                weights:
                  build: 40
                settings: {  }
              term_weight_widget_order:
                settings:
                  sort: ASC
        facets_professional_field:
          id: facets_professional_field
          table: search_api_index_job_posts
          field: facets_professional_field
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: facets_filter
          operator: '='
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: ''
            label: 'Professional field'
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: professional_field
            required: false
            remember: false
            multiple: true
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              talent: '0'
              company_admin: '0'
              company_recruiter: '0'
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          hierarchy: false
          label_display: visible
          facet:
            query_operator: or
            min_count: 1
            show_numbers: true
            processor_configs:
              hide_non_narrowing_result_processor:
                weights:
                  build: 40
                settings: {  }
              term_weight_widget_order:
                settings:
                  sort: ASC
        facets_company_name:
          id: facets_company_name
          table: search_api_index_job_posts
          field: facets_company_name
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: facets_filter
          operator: '='
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: ''
            label: 'Company name'
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: company_name
            required: false
            remember: false
            multiple: true
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              talent: '0'
              company_admin: '0'
              company_recruiter: '0'
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          hierarchy: false
          label_display: visible
          facet:
            query_operator: or
            min_count: 1
            show_numbers: true
            processor_configs:
              hide_non_narrowing_result_processor:
                weights:
                  build: 40
                settings: {  }
              display_value_widget_order:
                weights:
                  sort: 40
                settings:
                  sort: ASC
        facets_work_type:
          id: facets_work_type
          table: search_api_index_job_posts
          field: facets_work_type
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: facets_filter
          operator: '='
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: ''
            label: 'Work type'
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: work_type
            required: false
            remember: false
            multiple: true
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              talent: '0'
              company_admin: '0'
              company_recruiter: '0'
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          hierarchy: false
          label_display: visible
          facet:
            query_operator: or
            min_count: 1
            show_numbers: true
            processor_configs:
              hide_non_narrowing_result_processor:
                weights:
                  build: 40
                settings: {  }
              term_weight_widget_order:
                settings:
                  sort: ASC
        facets_language:
          id: facets_language
          table: search_api_index_job_posts
          field: facets_language
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: facets_filter
          operator: '='
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: ''
            label: Language
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: language
            required: false
            remember: false
            multiple: true
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              talent: '0'
              company_admin: '0'
              company_recruiter: '0'
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          hierarchy: false
          label_display: visible
          facet:
            query_operator: or
            min_count: 1
            show_numbers: true
            processor_configs:
              hide_non_narrowing_result_processor:
                weights:
                  build: 40
                settings: {  }
              term_weight_widget_order:
                settings:
                  sort: ASC
        language_with_fallback:
          id: language_with_fallback
          table: search_api_index_job_posts
          field: language_with_fallback
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: search_api_language
          operator: in
          value:
            '***LANGUAGE_language_interface***': '***LANGUAGE_language_interface***'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
      style:
        type: default
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
      row:
        type: fields
        options:
          default_field_elements: true
          inline: {  }
          separator: ''
          hide_empty: false
      query:
        type: search_api_query
        options:
          bypass_access: false
          skip_access: false
          preserve_facet_query_args: false
          query_tags: {  }
      relationships: {  }
      use_ajax: true
      header: {  }
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'user.node_grants:view'
        - user.permissions
      tags:
        - 'config:field.storage.group.field_company_logo'
        - 'config:field.storage.node.field_gross_net'
        - 'config:field.storage.node.field_reference_number'
        - 'config:field.storage.node.field_salary_from'
        - 'config:field.storage.node.field_salary_to'
        - 'config:search_api.index.job_posts'
        - 'search_api_list:job_posts'
  jobs_by_city:
    id: jobs_by_city
    display_title: 'By city'
    display_plugin: block
    position: 3
    display_options:
      title: 'Job offers'
      pager:
        type: load_more
        options:
          offset: 0
          pagination_heading_level: h4
          items_per_page: 6
          total_pages: null
          id: 0
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
          more_button_text: 'Load more'
          end_text: ''
          advanced:
            content_selector: ''
            pager_selector: ''
          effects:
            type: ''
            speed: slow
      arguments:
        tid:
          id: tid
          table: search_api_index_job_posts
          field: tid
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: search_api
          default_action: default
          exception:
            value: all
            title_enable: false
            title: All
          title_enable: false
          title: ''
          default_argument_type: node
          default_argument_options: {  }
          summary_options: {  }
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: false
          validate:
            type: none
            fail: 'not found'
          validate_options: {  }
          break_phrase: false
          not: false
      filters:
        status:
          id: status
          table: search_api_index_job_posts
          field: status
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: search_api_boolean
          operator: '='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        language_with_fallback:
          id: language_with_fallback
          table: search_api_index_job_posts
          field: language_with_fallback
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: search_api_language
          operator: in
          value:
            '***LANGUAGE_language_interface***': '***LANGUAGE_language_interface***'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
      defaults:
        title: false
        pager: false
        use_more: false
        use_more_always: false
        use_more_text: false
        link_display: false
        link_url: false
        arguments: false
        filters: false
        filter_groups: false
      display_description: ''
      use_more: false
      use_more_always: true
      use_more_text: 'See all'
      link_display: job_posts_listing
      link_url: ''
      display_extenders: {  }
      block_hide_empty: true
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'user.node_grants:view'
        - user.permissions
      tags:
        - 'config:field.storage.group.field_company_logo'
        - 'config:field.storage.node.field_gross_net'
        - 'config:field.storage.node.field_reference_number'
        - 'config:field.storage.node.field_salary_from'
        - 'config:field.storage.node.field_salary_to'
        - 'config:search_api.index.job_posts'
        - 'search_api_list:job_posts'
  jobs_by_company:
    id: jobs_by_company
    display_title: 'By company'
    display_plugin: block
    position: 3
    display_options:
      title: 'Job offers'
      pager:
        type: load_more
        options:
          offset: 0
          pagination_heading_level: h4
          items_per_page: 6
          total_pages: null
          id: 0
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
          more_button_text: 'Load more'
          end_text: ''
          advanced:
            content_selector: ''
            pager_selector: ''
          effects:
            type: ''
            speed: slow
      arguments:
        company_id:
          id: company_id
          table: search_api_index_job_posts
          field: company_id
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: search_api
          default_action: default
          exception:
            value: all
            title_enable: false
            title: All
          title_enable: false
          title: ''
          default_argument_type: group_id_from_url
          default_argument_options: {  }
          summary_options: {  }
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: false
          validate:
            type: none
            fail: 'not found'
          validate_options: {  }
          break_phrase: false
          not: false
      filters:
        status:
          id: status
          table: search_api_index_job_posts
          field: status
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: search_api_boolean
          operator: '='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        language_with_fallback:
          id: language_with_fallback
          table: search_api_index_job_posts
          field: language_with_fallback
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: search_api_language
          operator: in
          value:
            '***LANGUAGE_language_interface***': '***LANGUAGE_language_interface***'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
      defaults:
        title: false
        pager: false
        use_more: false
        use_more_always: false
        use_more_text: false
        link_display: false
        link_url: false
        arguments: false
        filters: false
        filter_groups: false
      display_description: ''
      use_more: false
      use_more_always: true
      use_more_text: 'See all'
      link_display: job_posts_listing
      link_url: ''
      display_extenders: {  }
      block_hide_empty: true
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - route
        - url
        - url.query_args
        - 'user.node_grants:view'
        - user.permissions
      tags:
        - 'config:field.storage.group.field_company_logo'
        - 'config:field.storage.node.field_gross_net'
        - 'config:field.storage.node.field_reference_number'
        - 'config:field.storage.node.field_salary_from'
        - 'config:field.storage.node.field_salary_to'
        - 'config:search_api.index.job_posts'
        - 'search_api_list:job_posts'
  jobs_latest_6:
    id: jobs_latest_6
    display_title: 'Latest 6'
    display_plugin: block
    position: 2
    display_options:
      title: ''
      pager:
        type: some
        options:
          offset: 0
          items_per_page: 6
      arguments: {  }
      filters:
        status:
          id: status
          table: search_api_index_job_posts
          field: status
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: search_api_boolean
          operator: '='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        language_with_fallback:
          id: language_with_fallback
          table: search_api_index_job_posts
          field: language_with_fallback
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: search_api_language
          operator: in
          value:
            '***LANGUAGE_language_interface***': '***LANGUAGE_language_interface***'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
      defaults:
        title: false
        pager: false
        use_more: false
        use_more_always: false
        use_more_text: false
        link_display: false
        link_url: false
        arguments: false
        filters: false
        filter_groups: false
      display_description: ''
      use_more: false
      use_more_always: true
      use_more_text: 'See all'
      link_display: job_posts_listing
      link_url: ''
      display_extenders: {  }
      block_hide_empty: true
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - 'user.node_grants:view'
        - user.permissions
      tags:
        - 'config:field.storage.group.field_company_logo'
        - 'config:field.storage.node.field_gross_net'
        - 'config:field.storage.node.field_reference_number'
        - 'config:field.storage.node.field_salary_from'
        - 'config:field.storage.node.field_salary_to'
        - 'config:search_api.index.job_posts'
        - 'search_api_list:job_posts'
  jobs_listing:
    id: jobs_listing
    display_title: 'Main listing'
    display_plugin: block
    position: 1
    display_options:
      filters:
        search_api_fulltext:
          id: search_api_fulltext
          table: search_api_index_job_posts
          field: search_api_fulltext
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: search_api_fulltext
          operator: or
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: search_api_fulltext_op
            label: ''
            description: ''
            use_operator: false
            operator: search_api_fulltext_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: search_api_fulltext
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              talent: '0'
              company_admin: '0'
              company_recruiter: '0'
            expose_fields: false
            placeholder: Searching
            searched_fields_id: search_api_fulltext_searched_fields
            value_maxlength: 128
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          parse_mode: terms
          min_length: null
          fields:
            - additional_benefits
            - annual_leave
            - company_name_search
            - development_and_training
            - education
            - family_and_social_support
            - field_reference_number
            - field_work_type_search
            - food_and_beverage
            - health_and_insurance
            - language_search
            - place_of_work_search
            - professional_field_search
            - relocation_and_transpor
            - remuneration_and_bonuses
            - sport_and_wellness
            - title
            - working_environment
            - working_hours
        status:
          id: status
          table: search_api_index_job_posts
          field: status
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: search_api_boolean
          operator: '='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        facets_place_of_work:
          id: facets_place_of_work
          table: search_api_index_job_posts
          field: facets_place_of_work
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: facets_filter
          operator: '='
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: ''
            label: 'Place of work'
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: place_of_work
            required: false
            remember: false
            multiple: true
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              talent: '0'
              company_admin: '0'
              company_recruiter: '0'
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          hierarchy: false
          label_display: visible
          facet:
            query_operator: or
            min_count: 1
            show_numbers: true
            processor_configs:
              hide_non_narrowing_result_processor:
                weights:
                  build: 40
                settings: {  }
              term_weight_widget_order:
                settings:
                  sort: ASC
        facets_professional_field:
          id: facets_professional_field
          table: search_api_index_job_posts
          field: facets_professional_field
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: facets_filter
          operator: '='
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: ''
            label: 'Professional field'
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: professional_field
            required: false
            remember: false
            multiple: true
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              talent: '0'
              company_admin: '0'
              company_recruiter: '0'
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          hierarchy: false
          label_display: visible
          facet:
            query_operator: or
            min_count: 1
            show_numbers: true
            processor_configs:
              hide_non_narrowing_result_processor:
                weights:
                  build: 40
                settings: {  }
              term_weight_widget_order:
                settings:
                  sort: ASC
        facets_company_name:
          id: facets_company_name
          table: search_api_index_job_posts
          field: facets_company_name
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: facets_filter
          operator: '='
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: ''
            label: 'Company name'
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: company_name
            required: false
            remember: false
            multiple: true
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              talent: '0'
              company_admin: '0'
              company_recruiter: '0'
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          hierarchy: false
          label_display: visible
          facet:
            query_operator: or
            min_count: 1
            show_numbers: true
            processor_configs:
              hide_non_narrowing_result_processor:
                weights:
                  build: 40
                settings: {  }
              display_value_widget_order:
                weights:
                  sort: 40
                settings:
                  sort: ASC
        facets_work_type:
          id: facets_work_type
          table: search_api_index_job_posts
          field: facets_work_type
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: facets_filter
          operator: '='
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: ''
            label: 'Work type'
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: work_type
            required: false
            remember: false
            multiple: true
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              talent: '0'
              company_admin: '0'
              company_recruiter: '0'
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          hierarchy: false
          label_display: visible
          facet:
            query_operator: or
            min_count: 1
            show_numbers: true
            processor_configs:
              hide_non_narrowing_result_processor:
                weights:
                  build: 40
                settings: {  }
              term_weight_widget_order:
                settings:
                  sort: ASC
        facets_language:
          id: facets_language
          table: search_api_index_job_posts
          field: facets_language
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: facets_filter
          operator: '='
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: ''
            label: Language
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: language
            required: false
            remember: false
            multiple: true
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              talent: '0'
              company_admin: '0'
              company_recruiter: '0'
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          hierarchy: false
          label_display: visible
          facet:
            query_operator: or
            min_count: 1
            show_numbers: true
            processor_configs:
              hide_non_narrowing_result_processor:
                weights:
                  build: 40
                settings: {  }
              term_weight_widget_order:
                settings:
                  sort: ASC
        language_with_fallback:
          id: language_with_fallback
          table: search_api_index_job_posts
          field: language_with_fallback
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: search_api_language
          operator: in
          value:
            '***LANGUAGE_language_interface***': '***LANGUAGE_language_interface***'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
      defaults:
        filters: false
        filter_groups: false
      display_description: ''
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'user.node_grants:view'
        - user.permissions
      tags:
        - 'config:field.storage.group.field_company_logo'
        - 'config:field.storage.node.field_gross_net'
        - 'config:field.storage.node.field_reference_number'
        - 'config:field.storage.node.field_salary_from'
        - 'config:field.storage.node.field_salary_to'
        - 'config:search_api.index.job_posts'
        - 'search_api_list:job_posts'
