uuid: 7569e8d1-5343-4fc5-85f9-8b027599110e
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_date
    - node.type.event
  module:
    - datetime_range
id: node.event.field_date
field_name: field_date
entity_type: node
bundle: event
label: Date
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: daterange
