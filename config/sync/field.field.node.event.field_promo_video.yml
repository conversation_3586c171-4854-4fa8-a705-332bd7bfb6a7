uuid: 44fc4428-09b7-49e5-8089-017f91dc2766
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_promo_video
    - media.type.remote_video
    - node.type.event
id: node.event.field_promo_video
field_name: field_promo_video
entity_type: node
bundle: event
label: 'Promo video'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:media'
  handler_settings:
    target_bundles:
      remote_video: remote_video
    sort:
      field: _none
      direction: ASC
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
