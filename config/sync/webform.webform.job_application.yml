uuid: 4fcfbec9-7bdf-4fa4-b936-5072ffbde5b1
langcode: en
status: open
dependencies: {  }
weight: 0
open: null
close: null
uid: 1
template: false
archive: false
id: job_application
title: 'Apply for the job'
description: ''
categories: {  }
elements: |-
  container:
    '#type': container
    '#attributes':
      class:
        - grid
        - grid-cols-2
        - gap-5
        - mb-5
    container_01:
      '#type': container
      '#attributes':
        class:
          - p-8
          - bg-white
          - border
          - border-solid
          - border-border-main
          - rounded-lg
          - js-form-wrapper
          - form-wrapper
      profile_summary:
        '#type': processed_text
        '#text': |-
          <div class="grid grid-cols-2 gap-20">
            <div>
              <div class="overflow-hidden mb-9">
                <img src="[current-user:talent:field_photo:medium]" class="rounded-full aspect-square object-cover w-115px">
              </div>
              <div>
                <h4 class="body-1 font-bold">[current-user:talent:field_first_name] [current-user:talent:field_last_name]</h4>
              </div>
              <div>[current-user:mail]</div>
              <div>[current-user:talent:field_phone:value]</div>
            </div>
            <div class="flex flex-col gap-6">
              <div>
                <h4 class="body-1 font-bold">Education</h4>
                <div>[current-user:talent:field_education]</div>
              </div>
              <div>
                <h4 class="body-1 font-bold">Languages</h4>
                <div>[current-user:talent:field_languages]</div>
              </div>
              <div>
                <h4 class="body-1 font-bold">Location</h4>
                <div>Current location – [current-user:talent:field_residence_place]</div>
                <div>Preferred location – [current-user:talent:field_interested_city]</div>
              </div>
            </div>
          </div>
        '#format': email_html
    container_02:
      '#type': container
      cv_existing:
        '#type': webform_entity_select
        '#title': 'Available CVs'
        '#title_display': none
        '#target_type': file
        '#selection_handler': views
        '#selection_settings':
          view:
            view_name: existing_cvs_on_apply
            display_name: entity_reference_1
            arguments:
              - '[current-user:uid]'
      cv_upload:
        '#type': managed_file
        '#title': 'Upload CV'
        '#title_display': none
        '#states':
          visible:
            ':input[name="cv_existing"]':
              value: upload
        '#max_filesize': '10'
        '#upload_location': 'private://talent-cv'
      certificates_upload:
        '#type': managed_file
        '#title': 'Upload certificates'
        '#multiple': 5
        '#title_display': none
        '#max_filesize': '10'
        '#file_extensions': 'jpg jpeg png webp pdf doc docx'
        '#upload_location': 'private://talent-certificates'
      certificates_existing:
        '#type': webform_entity_checkboxes
        '#title': 'Available certificates'
        '#title_display': none
        '#target_type': file
        '#selection_handler': views
        '#selection_settings':
          view:
            view_name: existing_certificates_on_apply
            display_name: entity_reference_1
            arguments:
              - '[current-user:uid]'
      reference_number:
        '#type': hidden
        '#title': 'Reference number'
        '#default_value': '[webform_submission:node:field_reference_number]'
  message:
    '#type': textarea
    '#title': 'Message / Cover letter to the company'
    '#placeholder': 'Start typing here...'
  actions:
    '#type': webform_actions
    '#title': 'Submit button(s)'
    '#attributes':
      class:
        - flex
        - justify-end
    '#submit__label': 'Send application'
css: ''
javascript: ''
settings:
  ajax: false
  ajax_scroll_top: form
  ajax_progress_type: ''
  ajax_effect: ''
  ajax_speed: null
  page: false
  page_submit_path: ''
  page_confirm_path: ''
  page_theme_name: ''
  form_title: source_entity_webform
  form_submit_once: false
  form_open_message: ''
  form_close_message: ''
  form_exception_message: ''
  form_previous_submissions: true
  form_confidential: false
  form_confidential_message: ''
  form_disable_remote_addr: false
  form_convert_anonymous: false
  form_prepopulate: false
  form_prepopulate_source_entity: false
  form_prepopulate_source_entity_required: false
  form_prepopulate_source_entity_type: ''
  form_unsaved: false
  form_disable_back: false
  form_submit_back: false
  form_disable_autocomplete: false
  form_novalidate: false
  form_disable_inline_errors: false
  form_required: false
  form_autofocus: false
  form_details_toggle: false
  form_reset: false
  form_access_denied: default
  form_access_denied_title: ''
  form_access_denied_message: ''
  form_access_denied_attributes: {  }
  form_file_limit: ''
  form_attributes: {  }
  form_method: ''
  form_action: ''
  share: false
  share_node: false
  share_theme_name: ''
  share_title: true
  share_page_body_attributes: {  }
  submission_label: ''
  submission_exception_message: ''
  submission_locked_message: ''
  submission_log: false
  submission_excluded_elements: {  }
  submission_exclude_empty: false
  submission_exclude_empty_checkbox: false
  submission_views: {  }
  submission_views_replace: {  }
  submission_user_columns: {  }
  submission_user_duplicate: false
  submission_access_denied: default
  submission_access_denied_title: ''
  submission_access_denied_message: ''
  submission_access_denied_attributes: {  }
  previous_submission_message: "<p>You've already applied to this position.</p>"
  previous_submissions_message: ''
  autofill: false
  autofill_message: ''
  autofill_excluded_elements: {  }
  wizard_progress_bar: true
  wizard_progress_pages: false
  wizard_progress_percentage: false
  wizard_progress_link: false
  wizard_progress_states: false
  wizard_start_label: ''
  wizard_preview_link: false
  wizard_confirmation: true
  wizard_confirmation_label: ''
  wizard_auto_forward: true
  wizard_auto_forward_hide_next_button: false
  wizard_keyboard: true
  wizard_track: ''
  wizard_prev_button_label: ''
  wizard_next_button_label: ''
  wizard_toggle: false
  wizard_toggle_show_label: ''
  wizard_toggle_hide_label: ''
  wizard_page_type: container
  wizard_page_title_tag: h2
  preview: 0
  preview_label: ''
  preview_title: ''
  preview_message: ''
  preview_attributes: {  }
  preview_excluded_elements: {  }
  preview_exclude_empty: true
  preview_exclude_empty_checkbox: false
  draft: none
  draft_multiple: false
  draft_auto_save: false
  draft_saved_message: ''
  draft_loaded_message: ''
  draft_pending_single_message: ''
  draft_pending_multiple_message: ''
  confirmation_type: inline
  confirmation_url: ''
  confirmation_title: ''
  confirmation_message: '<p>You application has been received successfully.</p>'
  confirmation_attributes: {  }
  confirmation_back: false
  confirmation_back_label: ''
  confirmation_back_attributes: {  }
  confirmation_exclude_query: false
  confirmation_exclude_token: false
  confirmation_update: false
  limit_total: null
  limit_total_interval: null
  limit_total_message: ''
  limit_total_unique: false
  limit_user: null
  limit_user_interval: null
  limit_user_message: ''
  limit_user_unique: false
  entity_limit_total: null
  entity_limit_total_interval: null
  entity_limit_user: null
  entity_limit_user_interval: null
  purge: none
  purge_days: null
  results_disabled: true
  results_disabled_ignore: true
  results_customize: false
  token_view: false
  token_update: false
  token_delete: false
  serial_disabled: false
access:
  create:
    roles:
      - authenticated
    users: {  }
    permissions: {  }
  view_any:
    roles: {  }
    users: {  }
    permissions: {  }
  update_any:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_any:
    roles: {  }
    users: {  }
    permissions: {  }
  purge_any:
    roles: {  }
    users: {  }
    permissions: {  }
  view_own:
    roles: {  }
    users: {  }
    permissions: {  }
  update_own:
    roles: {  }
    users: {  }
    permissions: {  }
  delete_own:
    roles: {  }
    users: {  }
    permissions: {  }
  administer:
    roles: {  }
    users: {  }
    permissions: {  }
  test:
    roles: {  }
    users: {  }
    permissions: {  }
  configuration:
    roles: {  }
    users: {  }
    permissions: {  }
handlers: {  }
variants: {  }
