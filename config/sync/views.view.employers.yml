uuid: 1c030639-22dd-4519-8aa9-39db594af681
langcode: en
status: true
dependencies:
  config:
    - field.storage.group.field_company_logo
    - group.type.company
    - image.style.medium
  module:
    - group
    - image
    - node
id: employers
label: Employers
module: views
description: ''
tag: ''
base_table: groups_field_data
base_field: id
display:
  default:
    id: default
    display_title: Default
    display_plugin: default
    position: 0
    display_options:
      title: Employers
      fields:
        field_company_logo:
          id: field_company_logo
          table: group__field_company_logo
          field: field_company_logo
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: image
          settings:
            image_link: ''
            image_style: medium
            image_loading:
              attribute: lazy
          group_column: entity_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        label:
          id: label
          table: groups_field_data
          field: label
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: null
          entity_field: label
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        status:
          id: status
          table: node_field_data
          field: status
          relationship: gc__node
          group_type: sum
          admin_label: ''
          entity_type: node
          entity_field: status
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: true
            text: "{% if status %}\r\n  ({{ status ~ ' ' ~ (status == 1 ? 'job offer' : 'job offers') | t }})\r\n{% endif %}"
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: boolean
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
          set_precision: false
          precision: 0
          decimal: .
          format_plural: 0
          format_plural_string: !!binary MQNAY291bnQ=
          prefix: ''
          suffix: ''
        view_group:
          id: view_group
          table: groups
          field: view_group
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: group
          plugin_id: entity_link
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: 'See {{ nid }} items'
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          text: Details
          output_url_as_text: true
          absolute: false
      pager:
        type: none
        options:
          offset: 0
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      access:
        type: none
        options: {  }
      cache:
        type: tag
        options: {  }
      empty: {  }
      sorts: {  }
      arguments: {  }
      filters:
        status:
          id: status
          table: groups_field_data
          field: status
          entity_type: group
          entity_field: status
          plugin_id: boolean
          value: '1'
          group: 1
          expose:
            operator: ''
        type:
          id: type
          table: groups_field_data
          field: type
          entity_type: group
          entity_field: type
          plugin_id: bundle
          value:
            company: company
          group: 1
        field_top_employer_value:
          id: field_top_employer_value
          table: group__field_top_employer
          field: field_top_employer_value
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: boolean
          operator: '='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
          2: OR
      style:
        type: default
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
      row:
        type: fields
        options:
          default_field_elements: true
          inline: {  }
          separator: ''
          hide_empty: false
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships:
        group_relationship_id:
          id: group_relationship_id
          table: groups_field_data
          field: group_relationship_id
          relationship: none
          group_type: group
          admin_label: 'Group relationship'
          entity_type: group
          plugin_id: group_to_group_relationship
          required: false
          group_relation_plugins:
            'group_node:job_post': 'group_node:job_post'
            job_application: '0'
            'group_node:city': '0'
            'group_node:event': '0'
            'group_node:landing_page': '0'
            'group_node:news': '0'
            'group_node:partner': '0'
            'group_node:speaker': '0'
            'group_node:team_member': '0'
            'group_node:webform': '0'
            group_membership: '0'
        gc__node:
          id: gc__node
          table: group_relationship_field_data
          field: gc__node
          relationship: group_relationship_id
          group_type: group
          admin_label: 'Group relationship Content'
          entity_type: group_relationship
          plugin_id: group_relationship_to_entity
          required: false
          group_relation_plugins:
            'group_node:job_post': 'group_node:job_post'
            'group_node:city': '0'
            'group_node:event': '0'
            'group_node:landing_page': '0'
            'group_node:news': '0'
            'group_node:partner': '0'
            'group_node:speaker': '0'
            'group_node:team_member': '0'
            'group_node:webform': '0'
      group_by: true
      header: {  }
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
      tags:
        - 'config:field.storage.group.field_company_logo'
  employers:
    id: employers
    display_title: 'All employers'
    display_plugin: block
    position: 2
    display_options:
      pager:
        type: full
        options:
          offset: 0
          pagination_heading_level: h4
          items_per_page: 12
          total_pages: 7
          id: 0
          tags:
            next: ››
            previous: ‹‹
            first: '« First'
            last: 'Last »'
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
          quantity: 9
      filters:
        status:
          id: status
          table: groups_field_data
          field: status
          entity_type: group
          entity_field: status
          plugin_id: boolean
          value: '1'
          group: 1
          expose:
            operator: ''
        type:
          id: type
          table: groups_field_data
          field: type
          entity_type: group
          entity_field: type
          plugin_id: bundle
          value:
            company: company
          group: 1
        field_top_employer_value:
          id: field_top_employer_value
          table: group__field_top_employer
          field: field_top_employer_value
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: boolean
          operator: '!='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
          2: OR
      defaults:
        pager: false
        filters: false
        filter_groups: false
      display_description: ''
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url.query_args
      tags:
        - 'config:field.storage.group.field_company_logo'
  top_employers:
    id: top_employers
    display_title: 'Top employers'
    display_plugin: block
    position: 1
    display_options:
      title: 'Top Employers'
      defaults:
        title: false
        filters: true
        filter_groups: true
        header: false
      display_description: ''
      header: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
      tags:
        - 'config:field.storage.group.field_company_logo'
