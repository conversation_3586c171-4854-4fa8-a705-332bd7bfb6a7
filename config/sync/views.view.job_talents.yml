uuid: 0116cf42-f8fe-400e-ae2c-186083f06869
langcode: en
status: true
dependencies:
  config:
    - field.storage.profile.field_cv
    - field.storage.profile.field_first_name
    - field.storage.profile.field_last_name
    - field.storage.profile.field_linkedin
    - field.storage.profile.field_phone
    - field.storage.profile.field_photo
    - field.storage.profile.field_residence_place
    - image.style.large
    - node.type.job_post
    - search_api.index.talents
    - user.role.administrator
    - user.role.company_recruiter
  module:
    - file
    - image
    - link
    - phone_number
    - search_api
    - user
id: job_talents
label: Talents
module: views
description: ''
tag: ''
base_table: search_api_index_talents
base_field: search_api_id
display:
  default:
    id: default
    display_title: Default
    display_plugin: default
    position: 0
    display_options:
      title: Talents
      fields:
        photo:
          id: photo
          table: search_api_index_talents
          field: photo
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: search_api_field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: image
          settings:
            image_link: ''
            image_style: large
            image_loading:
              attribute: lazy
          group_column: ''
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
          field_rendering: true
          fallback_handler: search_api
          fallback_options:
            link_to_item: false
            use_highlighting: false
            multi_type: separator
            multi_separator: ', '
        first_name:
          id: first_name
          table: search_api_index_talents
          field: first_name
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: search_api_field
          label: ''
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
          field_rendering: false
          fallback_handler: search_api
          fallback_options:
            link_to_item: false
            use_highlighting: false
            multi_type: separator
            multi_separator: ', '
        last_name:
          id: last_name
          table: search_api_index_talents
          field: last_name
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: search_api_field
          label: ''
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
          field_rendering: false
          fallback_handler: search_api
          fallback_options:
            link_to_item: false
            use_highlighting: false
            multi_type: separator
            multi_separator: ', '
        nothing_1:
          id: nothing_1
          table: views
          field: nothing
          relationship: none
          group_type: group
          admin_label: 'Profile datasource: Name (indexed field)'
          plugin_id: custom
          label: ''
          exclude: false
          alter:
            alter_text: true
            text: '{{ first_name }} {{ last_name }}'
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: field-content
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: 'views-field views-field-name'
          element_default_classes: false
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: false
        residence_place:
          id: residence_place
          table: search_api_index_talents
          field: residence_place
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: search_api_field
          label: 'Place of residence'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: '-'
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
          field_rendering: false
          fallback_handler: search_api
          fallback_options:
            link_to_item: false
            use_highlighting: false
            multi_type: separator
            multi_separator: ', '
        linkedin:
          id: linkedin
          table: search_api_index_talents
          field: linkedin
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: search_api_field
          label: 'LinkedIn profile'
          exclude: false
          alter:
            alter_text: true
            text: "{% set url = linkedin %}\r\n{% set segments = url|split('/') %}\r\n{% set username = segments|last %}\r\n{{ username }}"
            make_link: true
            path: '{{ linkedin }}'
            absolute: true
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: _blank
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: '-'
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: uri
          type: link
          settings:
            trim_length: 80
            url_only: false
            url_plain: false
            rel: '0'
            target: '0'
          group_column: ''
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
          field_rendering: false
          fallback_handler: search_api
          fallback_options:
            link_to_item: false
            use_highlighting: false
            multi_type: separator
            multi_separator: ', '
        phone:
          id: phone
          table: search_api_index_talents
          field: phone
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: search_api_field
          label: 'Phone number'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: '-'
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: phone_number_international
          settings:
            as_link: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
          field_rendering: false
          fallback_handler: search_api
          fallback_options:
            link_to_item: false
            use_highlighting: false
            multi_type: separator
            multi_separator: ', '
        cv:
          id: cv
          table: search_api_index_talents
          field: cv
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: search_api_field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: file_default
          settings:
            use_description_as_link_text: false
          group_column: ''
          group_columns: {  }
          group_rows: true
          delta_limit: 5
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: '<br/>'
          field_api_classes: false
          field_rendering: true
          fallback_handler: search_api
          fallback_options:
            link_to_item: false
            use_highlighting: false
            multi_type: separator
            multi_separator: ', '
        nothing:
          id: nothing
          table: views
          field: nothing
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: custom
          label: ''
          exclude: false
          alter:
            alter_text: true
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: false
      pager:
        type: full
        options:
          offset: 0
          pagination_heading_level: h4
          items_per_page: 10
          total_pages: null
          id: 0
          tags:
            next: ››
            previous: ‹‹
            first: '« First'
            last: 'Last »'
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
          quantity: 9
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      access:
        type: role
        options:
          role:
            administrator: administrator
            company_recruiter: company_recruiter
      cache:
        type: search_api_none
        options: {  }
      empty:
        area_text_custom:
          id: area_text_custom
          table: views
          field: area_text_custom
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text_custom
          empty: true
          content: 'No results found.'
          tokenize: false
      sorts: {  }
      arguments:
        'null':
          id: 'null'
          table: views
          field: 'null'
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: 'null'
          default_action: default
          exception:
            value: all
            title_enable: false
            title: All
          title_enable: false
          title: ''
          default_argument_type: node
          default_argument_options: {  }
          summary_options:
            base_path: ''
            count: true
            override: false
            items_per_page: 25
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: true
          validate:
            type: 'entity:node'
            fail: 'not found'
          validate_options:
            bundles:
              job_post: job_post
            access: false
            operation: update
            multiple: 0
          must_not_be: false
      filters:
        search_api_fulltext:
          id: search_api_fulltext
          table: search_api_index_talents
          field: search_api_fulltext
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: search_api_fulltext
          operator: or
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: search_api_fulltext_op
            label: ''
            description: ''
            use_operator: false
            operator: search_api_fulltext_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: search
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              talent: '0'
              company_admin: '0'
              company_recruiter: '0'
            expose_fields: false
            placeholder: 'Search by keyword'
            searched_fields_id: search_api_fulltext_searched_fields
            value_maxlength: 127
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          parse_mode: terms
          min_length: null
          fields: {  }
        profile_consent:
          id: profile_consent
          table: search_api_index_talents
          field: profile_consent
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: search_api_boolean
          operator: '='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      style:
        type: grid_responsive
        options:
          grouping: {  }
          columns: 2
          cell_min_width: 100
          grid_gutter: 10
          alignment: horizontal
      row:
        type: fields
        options:
          default_field_elements: true
          inline: {  }
          separator: ''
          hide_empty: false
      query:
        type: search_api_query
        options:
          bypass_access: false
          skip_access: false
          preserve_facet_query_args: false
          query_tags: {  }
      relationships: {  }
      use_ajax: false
      header: {  }
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - user.roles
      tags:
        - 'config:field.storage.profile.field_cv'
        - 'config:field.storage.profile.field_first_name'
        - 'config:field.storage.profile.field_last_name'
        - 'config:field.storage.profile.field_linkedin'
        - 'config:field.storage.profile.field_phone'
        - 'config:field.storage.profile.field_photo'
        - 'config:field.storage.profile.field_residence_place'
        - 'config:search_api.index.talents'
        - 'search_api_list:talents'
  talents:
    id: talents
    display_title: Page
    display_plugin: page
    position: 1
    display_options:
      display_extenders: {  }
      path: node/%node/talents
      menu:
        type: tab
        title: Talents
        description: ''
        weight: 0
        expanded: false
        menu_name: main
        parent: ''
        context: '0'
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - user.roles
      tags:
        - 'config:field.storage.profile.field_cv'
        - 'config:field.storage.profile.field_first_name'
        - 'config:field.storage.profile.field_last_name'
        - 'config:field.storage.profile.field_linkedin'
        - 'config:field.storage.profile.field_phone'
        - 'config:field.storage.profile.field_photo'
        - 'config:field.storage.profile.field_residence_place'
        - 'config:search_api.index.talents'
        - 'search_api_list:talents'
