uuid: f3fc80b9-f96c-4952-a030-3f045a277e59
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_region
    - node.type.city
id: node.city.field_region
field_name: field_region
entity_type: node
bundle: city
label: Region
description: ''
required: true
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: views
  handler_settings:
    view:
      view_name: regions
      display_name: entity_reference_cities
      arguments: {  }
field_type: entity_reference
