uuid: 3995f6f7-c2cc-41a1-8409-b0236cd7f5a5
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_languages
    - node.type.job_post
    - taxonomy.vocabulary.languages
id: node.job_post.field_languages
field_name: field_languages
entity_type: node
bundle: job_post
label: Languages
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      languages: languages
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
