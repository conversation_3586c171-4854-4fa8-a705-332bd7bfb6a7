uuid: bd027a8a-439e-4722-8887-67b35463a1c5
langcode: bg
status: true
dependencies:
  config:
    - field.field.node.landing_page.field_components
    - field.field.node.landing_page.field_hero_button
    - field.field.node.landing_page.field_hero_subtitle
    - field.field.node.landing_page.field_hero_theme
    - field.field.node.landing_page.field_metatags
    - node.type.landing_page
  module:
    - entity_reference_revisions
    - metatag
    - user
id: node.landing_page.default
targetEntityType: node
bundle: landing_page
mode: default
content:
  field_components:
    type: entity_reference_revisions_entity_view
    label: hidden
    settings:
      view_mode: default
      link: ''
    third_party_settings: {  }
    weight: 0
    region: content
  field_metatags:
    type: metatag_empty_formatter
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 2
    region: content
  links:
    settings: {  }
    third_party_settings: {  }
    weight: 1
    region: content
hidden:
  addtoany: true
  entitygroupfield: true
  field_hero_button: true
  field_hero_subtitle: true
  field_hero_theme: true
  langcode: true
  node_read_time: true
  private_message_link: true
  search_api_attachments: true
  search_api_excerpt: true
