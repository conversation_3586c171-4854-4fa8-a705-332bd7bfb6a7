uuid: 4a5cc860-d8a1-4cc4-ae1a-187c61d5ffbf
langcode: bg
status: true
dependencies:
  config:
    - field.storage.paragraph.field_markup_type
    - paragraphs.paragraphs_type.markup
  module:
    - options
id: paragraph.markup.field_markup_type
field_name: field_markup_type
entity_type: paragraph
bundle: markup
label: Type
description: ''
required: true
translatable: false
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: list_string
