uuid: 3225f7be-bb39-499f-8322-502914fc4669
langcode: bg
status: true
dependencies:
  config:
    - taxonomy.vocabulary.city
  module:
    - taxonomy
    - user
id: regions
label: Regions
module: views
description: ''
tag: ''
base_table: taxonomy_term_field_data
base_field: tid
display:
  default:
    id: default
    display_title: Default
    display_plugin: default
    position: 0
    display_options:
      fields:
        name_1:
          id: name_1
          table: taxonomy_term_field_data
          field: name
          relationship: parent_target_id
          group_type: group
          admin_label: ''
          entity_type: taxonomy_term
          entity_field: name
          plugin_id: term_name
          label: ''
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
          convert_spaces: false
        name:
          id: name
          table: taxonomy_term_field_data
          field: name
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: taxonomy_term
          entity_field: name
          plugin_id: term_name
          label: ''
          exclude: false
          alter:
            alter_text: true
            text: '<strong>{{ name }}</strong> ({{ name_1 }})'
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: false
            ellipsis: false
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
          convert_spaces: false
      pager:
        type: mini
        options:
          offset: 0
          pagination_heading_level: h4
          items_per_page: 10
          total_pages: null
          id: 0
          tags:
            next: ››
            previous: ‹‹
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
      exposed_form:
        type: basic
        options:
          submit_button: Търси
          reset_button: false
          reset_button_label: Рестартирай
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      access:
        type: perm
        options:
          perm: 'access content'
      cache:
        type: tag
        options: {  }
      empty: {  }
      sorts: {  }
      arguments: {  }
      filters:
        status:
          id: status
          table: taxonomy_term_field_data
          field: status
          entity_type: taxonomy_term
          entity_field: status
          plugin_id: boolean
          value: '1'
          group: 1
          expose:
            operator: ''
        vid:
          id: vid
          table: taxonomy_term_field_data
          field: vid
          entity_type: taxonomy_term
          entity_field: vid
          plugin_id: bundle
          value:
            city: city
        field_regional_city_value:
          id: field_regional_city_value
          table: taxonomy_term__field_regional_city
          field: field_regional_city_value
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: boolean
          operator: '='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      style:
        type: default
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
          uses_fields: false
      row:
        type: fields
        options:
          default_field_elements: true
          inline: {  }
          separator: ''
          hide_empty: false
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships:
        parent_target_id:
          id: parent_target_id
          table: taxonomy_term__parent
          field: parent_target_id
          relationship: none
          group_type: group
          admin_label: Parent
          entity_type: taxonomy_term
          entity_field: parent
          plugin_id: standard
          required: false
      header: {  }
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url.query_args
        - user.permissions
      tags: {  }
  entity_reference_cities:
    id: entity_reference_cities
    display_title: 'Entity Reference'
    display_plugin: entity_reference
    position: 1
    display_options:
      style:
        type: entity_reference
        options:
          search_fields:
            name: name
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - user.permissions
      tags: {  }
  entity_reference_cities_second_level:
    id: entity_reference_cities_second_level
    display_title: 'Cities - second level'
    display_plugin: entity_reference
    position: 2
    display_options:
      filters:
        status:
          id: status
          table: taxonomy_term_field_data
          field: status
          entity_type: taxonomy_term
          entity_field: status
          plugin_id: boolean
          value: '1'
          group: 1
          expose:
            operator: ''
        vid:
          id: vid
          table: taxonomy_term_field_data
          field: vid
          entity_type: taxonomy_term
          entity_field: vid
          plugin_id: bundle
          value:
            city: city
        tid_raw:
          id: tid_raw
          table: taxonomy_term_field_data
          field: tid_raw
          relationship: parent_target_id
          group_type: group
          admin_label: ''
          entity_type: taxonomy_term
          plugin_id: numeric
          operator: 'not empty'
          value:
            min: ''
            max: ''
            value: ''
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
      style:
        type: entity_reference
        options:
          search_fields:
            name: name
            name_1: '0'
      defaults:
        relationships: true
        filters: false
        filter_groups: false
      display_description: ''
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - user.permissions
      tags: {  }
