attributes:
  container_class:
    label: 'Container class(es)'
    description: 'CSS class for the menu list item (&lt;li&gt;). Separate multiple classes by space.'
  class:
    label: 'Link class(es)'
    description: 'CSS class for the link (&lt;a href&gt;). Separate multiple classes by space.'
  rel:
    label: 'Link rel'
    description: 'Rel attribute for the link.'
  target:
    label: 'Link target'
    description: ''
    options:
      _blank: 'New window (_blank)'
      _self: 'Same window (_self)'
    default_value: ''
