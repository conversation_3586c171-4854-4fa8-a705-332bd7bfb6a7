uuid: ecb6318f-2ffc-4865-9d6e-95885bb88516
langcode: en
status: true
dependencies:
  module:
    - menu_ui
    - scheduler
    - toc_js
third_party_settings:
  menu_ui:
    available_menus: {  }
    parent: ''
  scheduler:
    expand_fieldset: always
    fields_display_mode: fieldset
    publish_enable: true
    publish_past_date: error
    publish_past_date_created: true
    publish_required: false
    publish_revision: false
    publish_touch: false
    show_message_after_update: true
    unpublish_enable: false
    unpublish_required: false
    unpublish_revision: false
  toc_js:
    toc_js_active: 1
    title: 'Table of contents'
    title_tag: div
    title_classes: 'toc-title,h2'
    selectors: 'h2,h3'
    selectors_minimum: '0'
    container: .event--full
    prefix: toc
    list_type: ul
    list_classes: 'body-2 text-text-main-lighter'
    li_classes: ''
    inheritable_classes: ''
    classes: ''
    heading_classes: ''
    skip_invisible_headings: 0
    use_heading_html: 0
    collapsible_items: 0
    collapsible_expanded: 1
    back_to_top: 0
    back_to_top_label: 'Back to top'
    back_to_top_selector: ''
    heading_focus: 0
    back_to_toc: 0
    back_to_toc_label: 'Back to ToC'
    smooth_scrolling: 1
    scroll_to_offset: ''
    highlight_on_scroll: 1
    highlight_offset: '100'
    sticky: 1
    sticky_offset: 2rem
    toc_container: ''
    ajax_page_updates: 0
    observable_selector: ''
name: Event
type: event
description: null
help: null
new_revision: true
preview_mode: 0
display_submitted: false
