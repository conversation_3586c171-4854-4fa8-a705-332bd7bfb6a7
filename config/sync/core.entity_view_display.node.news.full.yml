uuid: eb0b0020-cfb6-4eb7-862e-52e5292e382a
langcode: en
status: false
dependencies:
  config:
    - core.entity_view_mode.node.full
    - field.field.node.news.field_event
    - field.field.node.news.field_formatted_description
    - field.field.node.news.field_gallery
    - field.field.node.news.field_image_media
    - field.field.node.news.field_summary
    - node.type.news
  module:
    - text
    - user
id: node.news.full
targetEntityType: node
bundle: news
mode: full
content:
  field_event:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: 111
    region: content
  field_formatted_description:
    type: text_default
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 102
    region: content
  field_gallery:
    type: entity_reference_entity_view
    label: above
    settings:
      view_mode: default
      link: false
    third_party_settings: {  }
    weight: 109
    region: content
  field_image_media:
    type: entity_reference_entity_view
    label: above
    settings:
      view_mode: default
      link: false
    third_party_settings: {  }
    weight: 103
    region: content
  field_summary:
    type: basic_string
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 110
    region: content
  links:
    settings: {  }
    third_party_settings: {  }
    weight: 100
    region: content
hidden:
  addtoany: true
  entitygroupfield: true
  langcode: true
  node_read_time: true
  private_message_link: true
  reading_time: true
  search_api_excerpt: true
  toc_js: true
