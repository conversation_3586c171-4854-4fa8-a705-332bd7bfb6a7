uuid: 0a10d560-01eb-4875-9e8a-0d689947fe81
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_cover_image
    - media.type.image
    - node.type.event
id: node.event.field_cover_image
field_name: field_cover_image
entity_type: node
bundle: event
label: 'Cover image'
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:media'
  handler_settings:
    target_bundles:
      image: image
    sort:
      field: _none
      direction: ASC
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
