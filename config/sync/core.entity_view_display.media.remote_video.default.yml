uuid: c3df18bd-23a1-4ffc-a1e0-9d8c574b509f
langcode: en
status: true
dependencies:
  config:
    - field.field.media.remote_video.field_category
    - field.field.media.remote_video.field_description_long
    - field.field.media.remote_video.field_media_oembed_video
    - image.style.large
    - media.type.remote_video
  module:
    - image
id: media.remote_video.default
targetEntityType: media
bundle: remote_video
mode: default
content:
  field_description_long:
    type: basic_string
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 1
    region: content
  thumbnail:
    type: image
    label: hidden
    settings:
      image_link: content
      image_style: large
      image_loading:
        attribute: lazy
    third_party_settings: {  }
    weight: 0
    region: content
hidden:
  addtoany: true
  created: true
  field_category: true
  field_media_oembed_video: true
  langcode: true
  name: true
  search_api_attachments: true
  search_api_excerpt: true
  uid: true
