uuid: d480b455-c1a1-40ec-b34b-ac496f5c927a
langcode: en
status: true
dependencies:
  config:
    - field.storage.group.field_address
    - group.type.company
  module:
    - address
id: group.company.field_address
field_name: field_address
entity_type: group
bundle: company
label: Адрес
description: ''
required: false
translatable: false
default_value:
  -
    langcode: en
    country_code: BG
    administrative_area: null
    locality: ''
    dependent_locality: null
    postal_code: ''
    sorting_code: null
    address_line1: ''
    address_line2: null
    address_line3: null
    organization: null
    given_name: null
    additional_name: null
    family_name: null
default_value_callback: ''
settings:
  available_countries:
    BG: BG
  langcode_override: ''
  field_overrides:
    givenName:
      override: hidden
    additionalName:
      override: hidden
    familyName:
      override: hidden
    organization:
      override: hidden
    addressLine2:
      override: hidden
    addressLine3:
      override: hidden
    sortingCode:
      override: hidden
    dependentLocality:
      override: hidden
    administrativeArea:
      override: hidden
  fields: {  }
field_type: address
