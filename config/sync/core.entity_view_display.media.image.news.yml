uuid: 438bacc2-dcea-46da-b71a-cb57a4157e55
langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.media.news
    - field.field.media.image.field_description
    - field.field.media.image.field_media_image
    - image.style.large
    - media.type.image
  module:
    - image
id: media.image.news
targetEntityType: media
bundle: image
mode: news
content:
  field_description:
    type: string
    label: hidden
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 1
    region: content
  field_media_image:
    type: image
    label: visually_hidden
    settings:
      image_link: ''
      image_style: large
      image_loading:
        attribute: lazy
    third_party_settings: {  }
    weight: 0
    region: content
hidden:
  addtoany: true
  created: true
  langcode: true
  name: true
  search_api_excerpt: true
  thumbnail: true
  uid: true
