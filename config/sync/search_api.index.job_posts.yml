uuid: 383851ec-5880-42b4-9d15-eac5ac6b7f27
langcode: en
status: true
dependencies:
  config:
    - field.storage.group.field_company_logo
    - field.storage.node.field_additional_benefits
    - field.storage.node.field_annual_leave
    - field.storage.node.field_development_and_training
    - field.storage.node.field_education
    - field.storage.node.field_family_and_social_support
    - field.storage.node.field_food_and_beverage
    - field.storage.node.field_gross_net
    - field.storage.node.field_health_and_insurance
    - field.storage.node.field_languages
    - field.storage.node.field_place_of_work
    - field.storage.node.field_professional_field
    - field.storage.node.field_reference_number
    - field.storage.node.field_relocation_and_transport
    - field.storage.node.field_remuneration_and_bonuses
    - field.storage.node.field_salary_from
    - field.storage.node.field_salary_to
    - field.storage.node.field_sport_and_wellness
    - field.storage.node.field_work_type
    - field.storage.node.field_working_environment
    - field.storage.node.field_working_hours
    - search_api.server.bwy_solr_server
  module:
    - entitygroupfield
    - group
    - node
    - scheduler
    - search_api_solr
    - taxonomy
third_party_settings:
  search_api_solr:
    finalize: false
    commit_before_finalize: false
    commit_after_finalize: false
    debug_finalize: false
    highlighter:
      maxAnalyzedChars: 51200
      fragmenter: gap
      usePhraseHighlighter: true
      highlightMultiTerm: true
      preserveMulti: false
      regex:
        slop: 0.5
        pattern: blank
        maxAnalyzedChars: 10000
      highlight:
        mergeContiguous: false
        requireFieldMatch: false
        snippets: 3
        fragsize: 0
    mlt:
      mintf: 1
      mindf: 1
      maxdf: 0
      maxdfpct: 0
      minwl: 0
      maxwl: 0
      maxqt: 100
      maxntp: 2000
      boost: false
      interestingTerms: none
    term_modifiers:
      slop: 3
      fuzzy: 1
      fuzzy_analyzer: true
    advanced:
      index_prefix: ''
      collection: ''
      timezone: ''
    multilingual:
      limit_to_content_language: true
      include_language_independent: true
      use_language_undefined_as_fallback_language: true
      specific_languages:
        en: en
        bg: bg
      use_universal_collation: false
id: job_posts
name: 'Job posts'
description: ''
read_only: false
field_settings:
  additional_benefits:
    label: 'Допълнителни придобивки » Taxonomy term » Име'
    datasource_id: 'entity:node'
    property_path: 'field_additional_benefits:entity:name'
    type: text
    boost: 0.6
    dependencies:
      config:
        - field.storage.node.field_additional_benefits
      module:
        - taxonomy
  annual_leave:
    label: 'Годишен отпуск » Taxonomy term » Име'
    datasource_id: 'entity:node'
    property_path: 'field_annual_leave:entity:name'
    type: text
    boost: 0.6
    dependencies:
      config:
        - field.storage.node.field_annual_leave
      module:
        - taxonomy
  company_id:
    label: 'Company ID'
    datasource_id: 'entity:node'
    property_path: 'entitygroupfield:entity:gid:entity:id'
    type: integer
    dependencies:
      module:
        - entitygroupfield
        - group
  company_name:
    label: 'Company name'
    datasource_id: 'entity:node'
    property_path: 'entitygroupfield:entity:gid:entity:label'
    type: string
    dependencies:
      module:
        - entitygroupfield
        - group
  company_name_search:
    label: 'Company name'
    datasource_id: 'entity:node'
    property_path: 'entitygroupfield:entity:gid:entity:label'
    type: text
    dependencies:
      module:
        - entitygroupfield
        - group
  created:
    label: 'Authored on'
    datasource_id: 'entity:node'
    property_path: created
    type: date
    dependencies:
      module:
        - node
  development_and_training:
    label: 'Развитие и обучение » Taxonomy term » Име'
    datasource_id: 'entity:node'
    property_path: 'field_development_and_training:entity:name'
    type: text
    boost: 0.6
    dependencies:
      config:
        - field.storage.node.field_development_and_training
      module:
        - taxonomy
  education:
    label: 'Минимално изискуемо образование » Taxonomy term » Име'
    datasource_id: 'entity:node'
    property_path: 'field_education:entity:name'
    type: text
    boost: 0.6
    dependencies:
      config:
        - field.storage.node.field_education
      module:
        - taxonomy
  family_and_social_support:
    label: 'Семейство и социална подкрепа » Taxonomy term » Име'
    datasource_id: 'entity:node'
    property_path: 'field_family_and_social_support:entity:name'
    type: text
    boost: 0.6
    dependencies:
      config:
        - field.storage.node.field_family_and_social_support
      module:
        - taxonomy
  field_reference_number:
    label: 'Референтен номер в системата на работодателя'
    datasource_id: 'entity:node'
    property_path: field_reference_number
    type: text
    dependencies:
      config:
        - field.storage.node.field_reference_number
  field_salary_from:
    label: 'Salary from'
    datasource_id: 'entity:node'
    property_path: field_salary_from
    type: decimal
    dependencies:
      config:
        - field.storage.node.field_salary_from
  field_salary_to:
    label: 'Salary to'
    datasource_id: 'entity:node'
    property_path: field_salary_to
    type: decimal
    dependencies:
      config:
        - field.storage.node.field_salary_to
  field_work_type_search:
    label: 'Модел на работа » Taxonomy term » Име'
    datasource_id: 'entity:node'
    property_path: 'field_work_type:entity:name'
    type: text
    boost: 0.8
    dependencies:
      config:
        - field.storage.node.field_work_type
      module:
        - taxonomy
  food_and_beverage:
    label: 'Храна и напитки » Taxonomy term » Име'
    datasource_id: 'entity:node'
    property_path: 'field_food_and_beverage:entity:name'
    type: text
    boost: 0.6
    dependencies:
      config:
        - field.storage.node.field_food_and_beverage
      module:
        - taxonomy
  health_and_insurance:
    label: 'Здраве и осигуряване » Taxonomy term » Име'
    datasource_id: 'entity:node'
    property_path: 'field_health_and_insurance:entity:name'
    type: text
    boost: 0.6
    dependencies:
      config:
        - field.storage.node.field_health_and_insurance
      module:
        - taxonomy
  language:
    label: Language
    datasource_id: 'entity:node'
    property_path: 'field_languages:entity:name'
    type: string
    dependencies:
      config:
        - field.storage.node.field_languages
      module:
        - taxonomy
  language_search:
    label: 'Необходими езици » Taxonomy term » Име'
    datasource_id: 'entity:node'
    property_path: 'field_languages:entity:name'
    type: text
    boost: 0.8
    dependencies:
      config:
        - field.storage.node.field_languages
      module:
        - taxonomy
  language_with_fallback:
    label: 'Language (with fallback)'
    property_path: language_with_fallback
    type: string
  logo:
    label: 'Company logo'
    datasource_id: 'entity:node'
    property_path: 'entitygroupfield:entity:gid:entity:field_company_logo'
    type: integer
    dependencies:
      config:
        - field.storage.group.field_company_logo
      module:
        - entitygroupfield
        - group
  place_of_work:
    label: 'Place of work'
    datasource_id: 'entity:node'
    property_path: 'field_place_of_work:entity:name'
    type: string
    dependencies:
      config:
        - field.storage.node.field_place_of_work
      module:
        - taxonomy
  place_of_work_search:
    label: 'Месторабота / Населено място » Taxonomy term » Име'
    datasource_id: 'entity:node'
    property_path: 'field_place_of_work:entity:name'
    type: text
    boost: 0.8
    dependencies:
      config:
        - field.storage.node.field_place_of_work
      module:
        - taxonomy
  professional_field:
    label: 'Professional field'
    datasource_id: 'entity:node'
    property_path: 'field_professional_field:entity:name'
    type: string
    dependencies:
      config:
        - field.storage.node.field_professional_field
      module:
        - taxonomy
  professional_field_search:
    label: 'Професионална среда » Taxonomy term » Име'
    datasource_id: 'entity:node'
    property_path: 'field_professional_field:entity:name'
    type: text
    boost: 0.8
    dependencies:
      config:
        - field.storage.node.field_professional_field
      module:
        - taxonomy
  relocation_and_transpor:
    label: 'Релокация и транспорт » Taxonomy term » Име'
    datasource_id: 'entity:node'
    property_path: 'field_relocation_and_transport:entity:name'
    type: text
    boost: 0.6
    dependencies:
      config:
        - field.storage.node.field_relocation_and_transport
      module:
        - taxonomy
  remuneration_and_bonuses:
    label: 'Заплащане и бонуси » Taxonomy term » Име'
    datasource_id: 'entity:node'
    property_path: 'field_remuneration_and_bonuses:entity:name'
    type: text
    boost: 0.6
    dependencies:
      config:
        - field.storage.node.field_remuneration_and_bonuses
      module:
        - taxonomy
  salary_type:
    label: 'Salary type'
    datasource_id: 'entity:node'
    property_path: field_gross_net
    type: string
    dependencies:
      config:
        - field.storage.node.field_gross_net
  sport_and_wellness:
    label: 'Спорт и уелнес » Taxonomy term » Име'
    datasource_id: 'entity:node'
    property_path: 'field_sport_and_wellness:entity:name'
    type: text
    boost: 0.6
    dependencies:
      config:
        - field.storage.node.field_sport_and_wellness
      module:
        - taxonomy
  status:
    label: Published
    datasource_id: 'entity:node'
    property_path: status
    type: boolean
    dependencies:
      module:
        - node
  tid:
    label: 'Place of work » Taxonomy term » Term ID'
    datasource_id: 'entity:node'
    property_path: 'field_place_of_work:entity:tid'
    type: integer
    dependencies:
      config:
        - field.storage.node.field_place_of_work
      module:
        - taxonomy
  title:
    label: Title
    datasource_id: 'entity:node'
    property_path: title
    type: text
    dependencies:
      module:
        - node
  unpublish_on:
    label: 'Unpublish on'
    datasource_id: 'entity:node'
    property_path: unpublish_on
    type: date
    dependencies:
      module:
        - scheduler
  work_type:
    label: 'Work type'
    datasource_id: 'entity:node'
    property_path: 'field_work_type:entity:name'
    type: string
    dependencies:
      config:
        - field.storage.node.field_work_type
      module:
        - taxonomy
  working_environment:
    label: 'Работна среда » Taxonomy term » Име'
    datasource_id: 'entity:node'
    property_path: 'field_working_environment:entity:name'
    type: text
    dependencies:
      config:
        - field.storage.node.field_working_environment
      module:
        - taxonomy
  working_hours:
    label: 'Работно време (часове и режим) » Taxonomy term » Име'
    datasource_id: 'entity:node'
    property_path: 'field_working_hours:entity:name'
    type: text
    boost: 0.6
    dependencies:
      config:
        - field.storage.node.field_working_hours
      module:
        - taxonomy
datasource_settings:
  'entity:node':
    bundles:
      default: false
      selected:
        - job_post
    languages:
      default: true
      selected: {  }
processor_settings:
  add_url: {  }
  aggregated_field: {  }
  auto_aggregated_fulltext_field: {  }
  custom_value: {  }
  entity_status: {  }
  entity_type: {  }
  language_with_fallback: {  }
  rendered_item: {  }
  solr_date_range:
    weights:
      preprocess_index: 0
tracker_settings:
  default:
    indexing_order: fifo
options:
  cron_limit: 50
  delete_on_fail: true
  index_directly: true
  track_changes_in_references: true
server: bwy_solr_server
