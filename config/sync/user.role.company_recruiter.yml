uuid: dfd4e2eb-31e2-4e71-a481-37185f33aff5
langcode: en
status: true
dependencies:
  config:
    - filter.format.basic_html
    - filter.format.email_html
    - workflows.workflow.moderate_job_posts
  module:
    - content_moderation
    - filter
    - node
    - private_message
    - profile
    - scheduler
id: company_recruiter
label: 'Company recruiter'
weight: 5
is_admin: false
permissions:
  - 'access user profiles'
  - 'create recruiter profile'
  - 'delete own private message'
  - 'delete private message thread for all'
  - 'schedule publishing of nodes'
  - 'update own recruiter profile'
  - 'use moderate_job_posts transition archive'
  - 'use moderate_job_posts transition create_new_draft'
  - 'use moderate_job_posts transition publish'
  - 'use moderate_job_posts transition send_for_review'
  - 'use moderate_job_posts transition unpublish'
  - 'use text format basic_html'
  - 'use text format email_html'
  - 'view any profile'
  - 'view latest version'
  - 'view own profile'
  - 'view own recruiter profile'
  - 'view own unpublished content'
  - 'view scheduled content'
