uuid: ae0424aa-a0ce-4c84-972a-213a3c7af12a
langcode: bg
status: true
dependencies:
  config:
    - core.entity_view_mode.node.teaser
    - field.field.node.team_member.field_email
    - field.field.node.team_member.field_image_media
    - field.field.node.team_member.field_job_position
    - field.field.node.team_member.field_node_weight
    - field.field.node.team_member.field_phone_number
    - field.field.node.team_member.field_summary
    - node.type.team_member
  module:
    - media
    - phone_number
    - user
id: node.team_member.teaser
targetEntityType: node
bundle: team_member
mode: teaser
content:
  field_email:
    type: basic_string
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 2
    region: content
  field_image_media:
    type: media_thumbnail
    label: hidden
    settings:
      image_link: ''
      image_style: ''
      image_loading:
        attribute: lazy
    third_party_settings: {  }
    weight: 0
    region: content
  field_job_position:
    type: string
    label: hidden
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 1
    region: content
  field_phone_number:
    type: phone_number_international
    label: hidden
    settings:
      as_link: false
    third_party_settings: {  }
    weight: 3
    region: content
  field_summary:
    type: basic_string
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 4
    region: content
hidden:
  addtoany: true
  entitygroupfield: true
  field_node_weight: true
  langcode: true
  links: true
  node_read_time: true
  private_message_link: true
  search_api_attachments: true
  search_api_excerpt: true
