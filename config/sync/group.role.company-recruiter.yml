uuid: a09eec15-4e29-42bc-b8fa-effbf3a4257e
langcode: en
status: true
dependencies:
  config:
    - group.type.company
id: company-recruiter
label: Recruiter
weight: -8
admin: false
scope: individual
global_role: null
group_type: company
permissions:
  - 'create group_node:job_post entity'
  - 'delete any group_node:job_post entity'
  - 'delete own group_node:job_post entity'
  - 'update any group_node:job_post entity'
  - 'update own group_node:job_post entity'
  - 'view group'
  - 'view group_node:job_post entity'
  - 'view group_node:job_post relationship'
  - 'view job_application relationship'
  - 'view own unpublished group_node:job_post entity'
  - 'view unpublished group_node:job_post entity'
