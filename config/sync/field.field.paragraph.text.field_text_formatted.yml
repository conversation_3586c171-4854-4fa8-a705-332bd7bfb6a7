uuid: e44e310c-5698-468e-a55c-4f83ce83838f
langcode: bg
status: true
dependencies:
  config:
    - field.storage.paragraph.field_text_formatted
    - filter.format.advanced_html
    - filter.format.basic_html
    - filter.format.email_html
    - paragraphs.paragraphs_type.text
  module:
    - text
id: paragraph.text.field_text_formatted
field_name: field_text_formatted
entity_type: paragraph
bundle: text
label: Text
description: ''
required: true
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  allowed_formats:
    - advanced_html
    - basic_html
    - email_html
field_type: text_long
