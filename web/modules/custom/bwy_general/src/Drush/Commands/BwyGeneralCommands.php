<?php

namespace Drupal\bwy_general\Drush\Commands;

use Drupal\Core\Entity\EntityTypeManagerInterface;
use Drupal\taxonomy\Entity\Term;
use Drupal\taxonomy\Entity\Vocabulary;
use Drush\Attributes as CLI;
use Drush\Commands\AutowireTrait;
use Drush\Commands\DrushCommands;
use GuzzleHttp\ClientInterface;
use GuzzleHttp\Exception\RequestException;

/**
 * Drush commands for BWY General module.
 */
final class BwyGeneralCommands extends DrushCommands {

  use AutowireTrait;

  /**
   * The entity type manager.
   */
  private readonly EntityTypeManagerInterface $entityTypeManager;

  /**
   * The HTTP client.
   */
  private readonly ClientInterface $httpClient;

  /**
   * Constructs a new BwyGeneralCommands object.
   */
  public function __construct(
    EntityTypeManagerInterface $entityTypeManager,
    ClientInterface $httpClient,
  ) {
    parent::__construct();
    $this->entityTypeManager = $entityTypeManager;
    $this->httpClient = $httpClient;
  }

  /**
   * Import Bulgarian regions and cities from JSON.
   *
   * @param array $options
   *   Command options including 'limit' for testing.
   */
  #[CLI\Command(name: 'bwy:import-bulgarian-cities', aliases: ['bwy-import-cities'])]
  #[CLI\Option(name: 'limit', description: 'Limit the number of records to import for testing')]
  #[CLI\Usage(name: 'bwy:import-bulgarian-cities', description: 'Import all Bulgarian regions and cities')]
  #[CLI\Usage(name: 'bwy:import-bulgarian-cities --limit=10', description: 'Import first 10 records for testing')]
  public function importBulgarianCities($options = ['limit' => 0]): void {
    $this->logger()->notice('Starting import of Bulgarian regions and cities...');

    // Check if vocabulary exists.
    $vocabulary = Vocabulary::load('city');
    if (!$vocabulary) {
      $this->logger()->error('Vocabulary "city" not found. Please create it first.');
      return;
    }

    try {
      // Fetch JSON data.
      $response = $this->httpClient->request('GET', 'https://raw.githubusercontent.com/Kostadin/Places-in-Bulgaria/refs/heads/master/Places-in-Bulgaria.json');
      $json_data = json_decode($response->getBody()->getContents(), TRUE);

      if (!$json_data) {
        $this->logger()->error('Failed to parse JSON data.');
        return;
      }

      $imported_count = 0;
      $limit = (int) $options['limit'];

      foreach ($json_data as $region_name => $cities) {
        if ($limit > 0 && $imported_count >= $limit) {
          $this->logger()->notice("Reached limit of {$limit} records. Stopping import.");
          break;
        }

        $this->logger()->notice("Processing region: {$region_name}");

        // Get the system region name (with proper prefix).
        $system_region_name = $this->getSystemRegionName($region_name);
        $this->logger()->notice("  Using system name: {$system_region_name}");

        // Create or get region term.
        [$region_term, $region_created] = $this->getOrCreateTerm($system_region_name, $vocabulary->id());
        if ($region_created) {
          $imported_count++;
        }

        foreach ($cities as $city_data) {
          foreach ($city_data as $city_info) {
            if ($limit > 0 && $imported_count >= $limit) {
              break 2;
            }

            // Create city name with type (e.g., "гр. Банско", "с. Гостун").
            $city_full_name = $city_info['type'] . ' ' . $city_info['name'];

            // Create or get city term with region as parent.
            [, $city_created] = $this->getOrCreateTerm($city_full_name, $vocabulary->id(), $region_term->id());

            if ($city_created) {
              $imported_count++;
            }
          }
        }
      }

      $this->logger()->success("Import completed successfully. Created {$imported_count} new terms.");

    }
    catch (RequestException $e) {
      $this->logger()->error('Failed to fetch JSON data: ' . $e->getMessage());
    }
    catch (\Exception $e) {
      $this->logger()->error('Import failed: ' . $e->getMessage());
    }
  }

  /**
   * Get the system name for a region based on JSON name.
   *
   * @param string $json_region_name
   *   The region name from JSON.
   *
   * @return string
   *   The corresponding system region name.
   */
  protected function getSystemRegionName(string $json_region_name): string {
    // Mapping from JSON region names to system region names.
    $region_mapping = [
      'София' => 'обл. София-град',
      'София област' => 'София област',
      // Add other mappings as needed.
    ];

    // If we have a specific mapping, use it.
    if (isset($region_mapping[$json_region_name])) {
      return $region_mapping[$json_region_name];
    }

    // Default mapping: add "обл." prefix to region names.
    return 'обл. ' . $json_region_name;
  }

  /**
   * Get or create a taxonomy term.
   *
   * @param string $name
   *   The term name.
   * @param string $vocabulary_id
   *   The vocabulary ID.
   * @param int|null $parent_tid
   *   The parent term ID (optional).
   *
   * @return array
   *   Array containing the term and whether it was created (true) or already
   *   existed (false).
   */
  protected function getOrCreateTerm(string $name, string $vocabulary_id, ?int $parent_tid = NULL): array {
    // Check if term already exists with the same parent.
    $properties = [
      'name' => $name,
      'vid' => $vocabulary_id,
    ];

    if ($parent_tid) {
      $properties['parent'] = $parent_tid;
    }

    $terms = $this->entityTypeManager
      ->getStorage('taxonomy_term')
      ->loadByProperties($properties);

    if (!empty($terms)) {
      $term = reset($terms);
      $this->logger()->notice("    Term '{$name}' already exists (ID: {$term->id()})");
      return [$term, FALSE];
    }

    // Create new term.
    $term_data = [
      'name' => $name,
      'vid' => $vocabulary_id,
    ];

    if ($parent_tid) {
      $term_data['parent'] = [$parent_tid];
    }

    $term = Term::create($term_data);
    $term->save();

    $this->logger()->notice("    Created new term '{$name}' (ID: {$term->id()})");
    return [$term, TRUE];
  }

}
