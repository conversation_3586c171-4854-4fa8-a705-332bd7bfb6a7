<?php

namespace Drupal\bwy_job_application\Plugin\Block;

use Drupal\Core\Block\BlockBase;
use Drupal\Core\StringTranslation\StringTranslationTrait;

/**
 * Provides a 'Job candidates menu' block.
 *
 * @Block(
 *   id = "bwy_job_candidates_menu_block",
 *   admin_label = @Translation("Job candidates menu block"),
 *   category = @Translation("BWY")
 * )
 */
class JobCandidatesMenuBlock extends BlockBase {

  use StringTranslationTrait;

  /**
   * {@inheritdoc}
   */
  public function build() {
    $route_param = '';
    return [
      '#markup' => '
        <div>
          <div>
            <a href="/company/' . $route_param . '/job">' . $this->t('Back to Job posts') . '</a>
          </div>

          <div>
            <a href="/company/' . $route_param . '/job/' . $route_param . '/candidates">' . $this->t('Applicants by job post') . '</a>
            <a href="">' . $this->t('Potential candidates') . '</a>
          </div>
        </div>
      ',
      '#attributes' => ['class' => ['job-candidates-menu-block']],
    ];
  }

}
