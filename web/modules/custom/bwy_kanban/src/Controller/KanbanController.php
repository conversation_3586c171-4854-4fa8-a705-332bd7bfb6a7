<?php

namespace Dr<PERSON>al\bwy_kanban\Controller;

use <PERSON><PERSON><PERSON>\Core\Controller\ControllerBase;
use <PERSON><PERSON><PERSON>\Core\Datetime\DateFormatter;
use <PERSON><PERSON><PERSON>\Core\File\FileUrlGeneratorInterface;
use <PERSON><PERSON>al\Core\Mail\MailManagerInterface;
use <PERSON><PERSON><PERSON>\Core\Render\RendererInterface;
use <PERSON><PERSON><PERSON>\state_machine\Plugin\Workflow\WorkflowState;
use Drupal\views\Views;
use Drupal\workflows\State;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;

/**
 * Class Kanban Controller.
 */
class KanbanController extends ControllerBase {

  /**
   * {@inheritDoc}
   */
  public function __construct(protected DateFormatter $dateFormatter, protected MailManagerInterface $mailManager, protected RendererInterface $renderer, protected FileUrlGeneratorInterface $fileUrlGenerator) {
  }

  /**
   * {@inheritDoc}
   */
  public static function create(ContainerInterface $container) {
    return new self(
      $container->get('date.formatter'),
      $container->get('plugin.manager.mail'),
      $container->get('renderer'),
      $container->get('file_url_generator'),
    );
  }

  /**
   * Update state.
   *
   * @param string $view_id
   *   View id.
   * @param string $display_id
   *   Display id.
   * @param int $entity_id
   *   Entity id.
   * @param string $state_value
   *   State value.
   *
   * @return \Symfony\Component\HttpFoundation\JsonResponse
   *   Return json.
   *
   * @throws \Drupal\Component\Plugin\Exception\InvalidPluginDefinitionException
   * @throws \Drupal\Component\Plugin\Exception\PluginNotFoundException
   * @throws \Drupal\Core\Entity\EntityStorageException
   */
  public function updateState($view_id, $display_id, $entity_id = 0, $state_value = '') {
    $message = NULL;
    $data = [
      'success' => FALSE,
      'message' => $message,
    ];
    if (!$state_value && !is_numeric($entity_id)) {
      return new JsonResponse($data);
    }
    $view = Views::getView($view_id);
    $handler = $view->getHandler($display_id, 'filter', 'type');
    $entity_type = !empty($handler['entity_type']) ? $handler['entity_type'] : 'user';
    $style_plugin = $view->display_handler->getPlugin('style');
    $status_field = $style_plugin->options["status_field"];
    $entity = $this->entityTypeManager()->getStorage($entity_type)
      ->load($entity_id);
    $message = $this->getHistoryMessage($entity, $status_field, $state_value);
    if (!array_key_exists($state_value, $this->getAllowedValues($entity, $status_field))) {
      $data['message'] = $this->t(
        'New state @state is not a valid', ['@state' => $state_value]
      );
      return new JsonResponse($data);
    }
    $extractStatus = explode(':', $status_field);
    if (!empty($extractStatus[1])) {
      $status_field = $extractStatus[0];
    }
    // Save new status.
    $origin_state = $entity->get($status_field)->value;
    $entity->set($status_field, $state_value);

    $this->moduleHandler()->alter('kanban_change_status', $entity, $view, $origin_state);
    $entity->save();
    $data = [
      'success' => TRUE,
      'message' => $message,
    ];
    return new JsonResponse($data);
  }

  /**
   * Get the value allowed in the state field.
   *
   * @param object $entity
   *   Entity.
   * @param string $fieldName
   *   Field name.
   *
   * @return array
   *   Allow values.
   *
   * @throws \Drupal\Component\Plugin\Exception\InvalidPluginDefinitionException
   * @throws \Drupal\Component\Plugin\Exception\PluginNotFoundException
   */
  protected function getAllowedValues($entity, $fieldName) {
    $extractStatus = explode(':', $fieldName);
    if (!empty($extractStatus[1])) {
      // $fieldName = $extractStatus[0];
      $workflow_id = $extractStatus[1];
      $workflow = $this->entityTypeManager()->getStorage('workflow')
        ->load($workflow_id);
      return array_map([
        State::class,
        'labelCallback',
      ], $workflow->getTypePlugin()->getStates());
    }
    $statusFieldDefinition = $entity->get($fieldName)->getFieldDefinition();
    $statusFieldValues = $statusFieldDefinition->getSettings();
    $allowed_values = [];
    if (!empty($statusFieldValues["allowed_values"])) {
      $allowed_values = $statusFieldValues["allowed_values"];
    }
    elseif (!empty($statusFieldValues["workflow"])) {
      // phpcs:ignore
      $workflow_manager = \Drupal::service('plugin.manager.workflow');
      $workflow = $workflow_manager->createInstance($statusFieldValues["workflow"]);
      $states = $workflow->getStates();
      $allowed_values = array_map(function (WorkflowState $state) {
        return $state->getLabel();
      }, $states);
    }
    if (!empty($statusFieldValues["target_type"])) {
      $vid = current($statusFieldValues["handler_settings"]["target_bundles"]);
      $loadTermStatus = $this->entityTypeManager()->getStorage('taxonomy_term')
        ->loadTree($vid);
      foreach ($loadTermStatus as $term) {
        $allowed_values[$term->tid] = $term->name;
      }
    }
    return $allowed_values;
  }

  /**
   * Get message for log.
   *
   * @param object $entity
   *   Entity.
   * @param string $status_field
   *   Status field.
   * @param string $newStatus
   *   New status.
   *
   * @return \Drupal\Core\StringTranslation\TranslatableMarkup
   *   Array text history status.
   *
   * @throws \Drupal\Component\Plugin\Exception\InvalidPluginDefinitionException
   * @throws \Drupal\Component\Plugin\Exception\PluginNotFoundException
   */
  protected function getHistoryMessage($entity, $status_field, $newStatus) {
    $statusName = $this->getStatusName($entity, $status_field, $newStatus);
    return $this->t('@user change from @old to @new', [
      '@user' => $this->currentUser()->getDisplayName(),
      '@old' => $statusName['old'],
      '@new' => $statusName['new'],
    ]);
  }

  /**
   * Get status name.
   */
  protected function getStatusName($entity, $status_field, $newStatus) {
    $statusList = $this->getAllowedValues($entity, $status_field);
    $extractStatus = explode(':', $status_field);
    if (!empty($extractStatus[1])) {
      $status_field = $extractStatus[0];
    }
    $currentStatus = $entity->get($status_field)->getString();
    if (is_array($currentStatus)) {
      $currentStatus = current($currentStatus);
    }
    if (!empty($statusList[$currentStatus])) {
      $currentStatus = $statusList[$currentStatus];
    }
    if (!empty($statusList[$newStatus])) {
      $newStatus = $statusList[$newStatus];
    }
    return [
      'old' => $currentStatus,
      'new' => $newStatus,
    ];
  }

}
