<?php

namespace Drupal\bwy_kanban\Plugin\views\style;

use <PERSON><PERSON>al\Core\Cache\Cache;
use <PERSON><PERSON>al\Core\Form\FormStateInterface;
use <PERSON><PERSON>al\Core\StringTranslation\TranslatableMarkup;
use Drupal\views\Attribute\ViewsStyle;
use Drupal\views\Plugin\views\style\StylePluginBase;
use Drupal\views\Plugin\views\wizard\WizardInterface;

/**
 * Style plugin to render each item in a kanban table.
 *
 * @ingroup views_style_plugins
 */
#[ViewsStyle(
  id: "kanban",
  title: new TranslatableMarkup("Kanban"),
  help: new TranslatableMarkup("Displays rows in a kanban."),
  theme: "views_view_kanban",
  display_types: ["normal"],
)]
class Kanban extends StylePluginBase {

  /**
   * Does the style plugin for itself support to add fields to its output.
   *
   * @var bool
   */
  protected $usesFields = TRUE;

  /**
   * {@inheritdoc}
   */
  protected $usesRowPlugin = FALSE;

  /**
   * Contains the current active sort column.
   *
   * @var string
   */
  public $active;

  /**
   * Contains the current active sort order, either desc or asc.
   *
   * @var string
   */
  public $order;

  /**
   * Define Options.
   */
  protected function defineOptions() {
    $options = parent::defineOptions();
    $options['status_field'] = ['default' => ''];
    return $options;
  }

  /**
   * Render the given style.
   */
  public function buildOptionsForm(&$form, FormStateInterface $form_state) {
    $fields = $this->displayHandler->getHandlers('field');
    $labels = $this->displayHandler->getFieldLabels();
    $type = [];
    foreach ($fields as $field_name => $field) {
      if (!empty($field->options["type"])) {
        $type[$field->options["type"]][$field_name] = $labels[$field_name];
      }
    }
    $optionsStatus = [];
    if (!empty($type["entity_reference_label"])) {
      $optionsStatus += $type["entity_reference_label"];
    }
    if (!empty($type["list_default"])) {
      $optionsStatus += $type["list_default"];
    }
    $form['status_field'] = [
      '#type' => 'select',
      '#title' => $this->t('Status field'),
      '#description' => $this->t('Select a taxonomy field or list field'),
      '#options' => $optionsStatus,
      '#required' => TRUE,
      '#empty_option' => $this->t('- Select -'),
      '#default_value' => !empty($this->options['status_field']) ? $this->options['status_field'] : '',
    ];
  }

  /**
   * {@inheritdoc}
   */
  public function wizardSubmit(&$form, FormStateInterface $form_state, WizardInterface $wizard, &$display_options, $display_type) {
    // If any of the displays use the table style, make sure that the fields
    // always have a labels by unsetting the override.
    foreach ($display_options['default']['fields'] as &$field) {
      unset($field['label']);
    }
  }

  /**
   * {@inheritdoc}
   */
  public function getCacheMaxAge() {
    return Cache::PERMANENT;
  }

  /**
   * {@inheritdoc}
   */
  public function getCacheTags() {
    return [];
  }

}
