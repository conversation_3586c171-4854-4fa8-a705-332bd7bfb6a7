<?php

/**
 * @file
 * Theme for Kanban views.
 */

use Drupal\Core\Render\Markup;
use Drupal\Core\Template\Attribute;

/**
 * Template preprocess views kanban.
 *
 * @param array $variables
 *   Array variable.
 */
function template_preprocess_views_view_kanban(array &$variables) {
  $view = $variables['view'];
  $rows = $variables['rows'];
  $style = $view->style_plugin;
  $options = $style->options;
  $requestStack = \Drupal::request();
  $currentUser = \Drupal::currentUser();
  $variables["view_id"] = $view->storage->id();
  $variables["display_id"] = $variables["view"]->current_display;
  $variables["options"] = $options;

  $colors = [
    'primary',
    'warning',
    'success',
    'danger',
    'info',
    'dark',
    'secondary',
    'primary-subtle',
    'warning-subtle',
    'success-subtle',
    'danger-subtle',
    'info-subtle',
    'dark-subtle',
    'secondary-subtle',
    'light',
    'light-subtle',
    'white',
  ];

  $variables['view']->element['#attached']['library'][] = 'bwy_kanban/kanban';
  $columns = [];

  $entity_type_id = 'node';
  if (!empty($statusField = $options["status_field"]) && !empty($rows)) {
    $filters = $view->filter[$statusField . '_value'] ?? ($view->filter[$statusField . '_target_id'] ?? FALSE);
    $hideColumn = [];
    $showColumn = [];
    if (!empty($filters)) {
      if (!empty($filters->operator == 'not')) {
        $hideColumn = $filters->value;
      }
      if (!empty($filters->operator == 'or')) {
        $showColumn = $filters->value;
      }
    }
    $row = current($rows);
    $entity = property_exists($row, '_entity') ? $row->_entity : NULL;
    if (empty($entity)) {
      $row = end($rows);
      $entity = property_exists($row, '_entity') ? $row->_entity : NULL;
    }
    $entity_type_id = $entity?->getEntityTypeId();

    $extractStatus = explode(':', $options["status_field"]);
    if (!empty($extractStatus[1])) {
      $options["status_field"] = $extractStatus[0];
    }

    if ($entity) {
      $status_field = $entity->get($options["status_field"]);
      $fieldDefinition = $status_field->getFieldDefinition();
      $field_status_settings = $fieldDefinition->getSettings();
      $status_values = [];
      if (!empty($field_status_settings["allowed_values"])) {
        $status_values = $field_status_settings["allowed_values"];
        $prepopulate = 'edit' . '[' . $options["status_field"] . '][widget][0][value]';
      }
    }
    if (!empty($hideColumn)) {
      foreach ($hideColumn as $hide) {
        if (isset($status_values[$hide])) {
          unset($status_values[$hide]);
        }
      }
    }
    if (!empty($showColumn)) {
      foreach (array_keys($status_values) as $show) {
        if (!in_array($show, $showColumn)) {
          unset($status_values[$show]);
        }
      }
    }
    $i = -1;
    foreach ($status_values as $id => $status_value) {
      if (empty($colors[++$i])) {
        $i = 0;
      }
      $linkOptions = [
        'attributes' => ['class' => ['btn', 'btn-' . $colors[$i]]],
        'absolute' => TRUE,
        'query' => $requestStack->query->all(),
      ];
      $linkOptions['query']['destination'] = $requestStack->getRequestUri();
      if (!empty($prepopulate)) {
        $linkOptions['query'][$prepopulate] = $id;
      }
      $columns[$id] = [
        'header' => $status_value,
        'color' => $colors[$i],
        'rows' => [],
      ];
    }
  }
  $variables['default_row_class'] = !empty($options['default_row_class']);

  $linkViewOptions = [
    'attributes' => [
      'class' => [
        'btn',
        'btn-sm',
        'btn-default',
      ],
    ],
    'absolute' => TRUE,
    'query' => $requestStack->query->all(),
  ];
  $linkViewOptions['query']['destination'] = $requestStack->getRequestUri();

  foreach ($rows as $id => $row) {
    $entity = $row->_entity ?? NULL;
    if (!$entity) {
      continue;
    }
    $getFieldStatus = $entity->get($options["status_field"]);
    $status = current($getFieldStatus->getValue());
    $statusValue = 0;
    if (!empty($status['target_id'])) {
      $statusValue = $status['target_id'];
    }
    if (!empty($status['value'])) {
      $statusValue = $status['value'];
    }
    if (!empty($hideColumn) && in_array($statusValue, $hideColumn)) {
      continue;
    }
    if (!empty($showColumn) && !in_array($statusValue, $showColumn)) {
      continue;
    }
    if (!empty($columns[$statusValue]) && !empty($columns[$statusValue]['color'])) {
      $linkViewOptions['attributes']['class'][3] = 'btn-outline-' . $columns[$statusValue]['color'];
    }
    $entity_id = $entity->id();
    $linkViewOptions['attributes']['id'] = 'viewkanban' . $entity_id;
    $entity_type = $entity->getEntityType()->id();
    $variables['rows'][$id] = [
      'entity_id' => $entity_id,
      'entity_type' => $entity_type,
      'attributes' => new Attribute(),
      'content' => [
        '#row' => $row,
        '#view' => $view,
        '#options' => ['default_field_elements' => FALSE],
        '#theme' => [
          'views_view_fields__kanban',
          'views_view_fields',
        ],
      ],
    ];

    $linkView = Markup::create('<i class="bi bi-eye"></i> <span class="d-none">' . t('View') . '</span>');
    if ($entity_type_id != 'paragraph') {
      if ($entity->access('view', $currentUser)) {
        $linkViewOptions['attributes']['data-bs-title'] = t('View');
        $variables['rows'][$id]['view'] = $entity->toLink($linkView, 'canonical', $linkViewOptions);
      }
    }

    if (empty($columns[$statusValue])) {
      array_unshift($columns, [$statusValue => []]);
    }

    if ($row_class = $style->getRowClass($id)) {
      $variables['rows'][$id]['attributes']->addClass($row_class);
    }

    $columns[$statusValue]['rows'][$id] = $variables['rows'][$id];
  }
  $variables['columns'] = $columns;
}
