<?php

/**
 * @file
 * Kanban Views module help and theme functions.
 */

/**
 * Implements hook_theme().
 */
function bwy_kanban_theme($existing, $type, $theme, $path) {
  // Store Kanban preprocess theme functions in a separate .inc file.
  \Drupal::moduleHandler()
    ->loadInclude('bwy_kanban', 'inc', 'bwy_kanban.theme');

  return [
    'views_view_kanban' => [
      'file' => 'bwy_kanban.theme.inc',
    ],
  ];
}
