{#
/**
 * Default theme implementation for Views Kanban.
 *
 * Available variables:
 * - options: View plugin style options:
 *   - classes: CSS classes.
 *   - columns: Whether months should be nested inside years.
 *
 * @see template_preprocess_views_view_kanban()
 *
 * @ingroup themeable
 */
#}
{% set classes = [
  'views-view-kanban',
  'container-fluid',
  'mt-lg-3',
  options.classes
] %}

<div {{ attributes.addClass(classes) }} data-view_id="{{ view_id }}" data-display_id="{{ display_id }}">
  <div class="row flex-row flex-sm-nowrap py-3">
    {% for index, column in columns %}
      <div class="layout-column layout-column--quarter col-auto col-md-auto col-lg-3 h-100 kanban-col status-{{ index|replace({' ':''}) }}" data-col-status="{{ index|replace({' ':''}) }}">
        <div class="gin-layer-wrapper mb-3 border card position-relative border-{{ column.color }}-subtle">
          <div class="card-header bg-{{ column.color }} bg-gradient">
            <div class="panel__title card-title h5 mb-1 d-flex justify-content-between">
              <span>{{ column.header|capitalize }}</span>
              <span class="badge button--dismiss translate-middle bg-{{ column.color }} border border-white total-status">{{ column.rows|length }}</span>
            </div>
          </div>

          <div class="panel-body card__content-wrapper" data-color="{{ column.color }}">
            <div class="card-body card__content overflow-auto vh-100" droppable="true" data-value="{{ index }}">
              {% for row in column.rows %}
                <div class="card mb-3 cursor-grab shadow-sm" id="item{{ row.entity_id }}" data-id="{{ row.entity_id }}"
                  draggable="true" data-value="{{ index }}" data-type="{{ row.entity_type }}" data-point="{{ row.total }}">
                  <article {{ row.attributes.addClass('card-body claro-details__wrapper claro-details__wrapper--system-status-report') }}>
                    <div class="card-text mb-0">{{ row.content }}</div>

                    {% if(row.view) %}
                      <div class="action-link action-link--icon-show mt-2">
                        {{ row.view }}
                      </div>
                    {% endif %}
                  </article>
                </div>
              {% endfor %}
            </div>
          </div>
        </div>
      </div>
    {% endfor %}
  </div>
</div>
