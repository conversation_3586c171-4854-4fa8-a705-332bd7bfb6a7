<?php

/**
 * @file
 * Views preprocess functions.
 */

require_once dirname(__FILE__) . '/salary.inc';

/**
 * Implements template_preprocess_views_view_fields().
 */
function bwy_preprocess_views_view_fields(&$variables) {
  $view_id = $variables['view']->id();

  if ($view_id === 'job_posts') {
    $row = $variables['row'];
    $node = $row->_entity;

    // Store the node ID in variables for use in template.
    $variables['node_id'] = $node->id();

    // Get the title value using getString().
    $variables['link_text'] = $node->get('title')->getString();

    // Use the unified formatting function.
    $variables['salary_range'] = _bwy_format_salary_range(
      $node,
      'field_salary_from',
      'field_salary_to'
    );
  }
}

/**
 * Implements template_preprocess_views_view_kanban().
 */
function bwy_preprocess_views_view_kanban(&$variables) {
  foreach ($variables['rows'] as &$row) {
    /** @var Drupal\Core\Url $a */
    $view_url = $row['view']->getUrl();
    $view_link_options = $view_url->getOptions();
    unset($view_link_options['attributes']['data-bs-toggle']);
    unset($view_link_options['attributes']['data-bs-placement']);
    unset($view_link_options['attributes']['data-dialog-type']);
    unset($view_link_options['attributes']['data-dialog-options']);
    unset($view_link_options['attributes']['data-bs-title']);
    if (is_array($view_link_options['attributes']['class'])) {
      $view_link_options['attributes']['class'] = array_filter($view_link_options['attributes']['class'], function ($class) {
        return $class !== 'use-ajax';
      });
    }

    $view_url->setOptions($view_link_options);
  }
}
