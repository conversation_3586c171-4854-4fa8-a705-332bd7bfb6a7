<?php

/**
 * @file
 * Paragraph preprocess functions.
 */

use Drupal\views\Views;

/**
 * Implements hook_preprocess_paragraph().
 */
function bwy_preprocess_paragraph(&$variables) {
  $paragraph = $variables['paragraph'];
  $view_mode = $variables['view_mode'] ?? '';

  if ($paragraph->bundle() === 'employers') {
    $view = Views::getView('employers');
    if ($view) {
      $view->setDisplay('employers');
      $view->preExecute();
      $view->execute();
      $variables['employers'] = $view->render();
    }
  }

  // View modes that should NOT have the extra spacing class.
  $excluded_view_modes = ['media_large', 'media_small'];

  // Add the 'my-60px' class only if the current
  // view mode is not in the excluded list.
  if (!in_array($view_mode, $excluded_view_modes, TRUE)) {
    $variables['attributes']['class'][] = 'my-60px';
  }
}

/**
 * Implements hook_preprocess_paragraph().
 */
function bwy_preprocess_paragraph__schedule_slot(&$variables) {
  /** @var \Drupal\paragraphs\Entity\Paragraph $paragraph */
  $paragraph = $variables['paragraph'];

  // Get the referenced speaker entities.
  $variables['content']['speakers'] = [];
  if ($paragraph->hasField('field_nodes') && !$paragraph->get('field_nodes')->isEmpty()) {
    /** @var \Drupal\Core\Field\EntityReferenceFieldItemListInterface $speakers_reference_field */
    $speakers_reference_field = $paragraph->get('field_nodes');
    $variables['content']['speakers'] = $speakers_reference_field->referencedEntities();
  }
}

/**
 * Implements hook_preprocess_paragraph().
 */
function bwy_preprocess_paragraph__partners_listing(&$variables) {
  // Get entity type manager and entity storage.
  $entity_type_manager = \Drupal::entityTypeManager();
  $term_storage = $entity_type_manager->getStorage('taxonomy_term');
  $node_storage = $entity_type_manager->getStorage('node');

  // Load all partner categories ordered by weight.
  $category_query = $term_storage->getQuery()
    ->condition('vid', 'partner_category')
    ->condition('status', 1)
    ->sort('weight', 'ASC')
    ->accessCheck(TRUE);

  $category_ids = $category_query->execute();

  $variables['partner_categories'] = [];

  if (!empty($category_ids)) {
    $categories = $term_storage->loadMultiple($category_ids);

    foreach ($categories as $category) {
      /** @var \Drupal\taxonomy\Entity\Term $category */
      // Get the category theme (small or large).
      $category_theme = 'large';
      if ($category->hasField('field_partner_category_theme') && !$category->get('field_partner_category_theme')->isEmpty()) {
        $category_theme = $category->get('field_partner_category_theme')->value;
      }

      // Load partners for this category ordered by node_weight.
      $partner_query = $node_storage->getQuery()
        ->condition('type', 'partner')
        ->condition('status', 1)
        ->condition('field_partner_category', $category->id())
        ->sort('field_node_weight', 'ASC')
        ->accessCheck(TRUE);

      $partner_ids = $partner_query->execute();

      if (!empty($partner_ids)) {
        $partners = $node_storage->loadMultiple($partner_ids);

        // Prepare partner data with view mode.
        $partner_data = [];
        switch ($category_theme) {
          case 'large':
            $view_mode = 'media_large';
            break;

          case 'medium':
            $view_mode = 'media_medium';
            break;

          default:
            $view_mode = 'media_small';
            break;
        }

        foreach ($partners as $partner) {
          $partner_data[] = [
            'content' => \Drupal::entityTypeManager()
              ->getViewBuilder('node')
              ->view($partner, $view_mode),
          ];
        }

        $variables['partner_categories'][] = [
          'category' => $category,
          'theme' => $category_theme,
          'partners' => $partner_data,
        ];
      }
    }
  }
}

/**
 * Implements hook_theme_suggestions_paragraph_alter().
 */
function bwy_theme_suggestions_paragraph_alter(array &$suggestions, array $variables) {
  /** @var \Drupal\paragraphs\Entity\Paragraph $paragraph */
  $paragraph = $variables['elements']['#paragraph'];

  // Add theme suggestions for markup paragraphs based on field_markup_type.
  if ($paragraph->bundle() === 'markup') {
    if ($paragraph->hasField('field_markup_type') && !$paragraph->get('field_markup_type')->isEmpty()) {
      $markup_type = $paragraph->get('field_markup_type')->value;

      // Add the theme suggestion.
      $suggestions[] = 'paragraph__markup__' . $markup_type;
    }
  }
}
