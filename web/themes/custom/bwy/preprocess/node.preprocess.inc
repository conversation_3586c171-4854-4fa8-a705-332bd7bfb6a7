<?php

/**
 * @file
 * Node preprocess functions.
 */

use <PERSON><PERSON>al\block\Entity\Block;
use <PERSON>upal\Core\Datetime\DrupalDateTime;
use Drupal\Core\Language\LanguageInterface;
use Drupal\views\Views;

require_once dirname(__FILE__) . '/salary.inc';

/**
 * Implements hook_preprocess_node().
 */
function bwy_preprocess_node__news(&$variables) {
  if (in_array($variables['view_mode'], ['teaser', 'featured', 'full']) && !empty($variables['content']['field_image_media'][0])) {
    $variables['content']['field_image_media'][0]['#item_attributes']['class'][] = 'w-full';
    $variables['content']['field_image_media'][0]['#item_attributes']['class'][] = 'h-full';
    $variables['content']['field_image_media'][0]['#item_attributes']['class'][] = 'object-cover';
  }

  // Programmatically load and render the breadcrumb block for use in templates.
  $block = Block::load('system_breadcrumb_block');
  if ($block) {
    $view_builder = \Drupal::entityTypeManager()->getViewBuilder('block');
    $variables['breadcrumb_block'] = $view_builder->view($block);
  }
}

/**
 * Implements hook_preprocess_node().
 */
function bwy_preprocess_node__event(&$variables) {
  /** @var \Drupal\node\Entity\Node $node */
  $node = $variables['node'];

  // Ensure 'field_date' and 'field_date_show_month_only'
  // fields exist and are not empty.
  if ($node->hasField('field_date') && !$node->get('field_date')->isEmpty() &&
      $node->hasField('field_date_show_month_only') &&
      !$node->get('field_date_show_month_only')->isEmpty()) {

    $show_month_only = (bool) $node->get('field_date_show_month_only')->value;

    $start_date_iso = $node->get('field_date')->value;
    $end_date_iso = $node->get('field_date')->end_value;

    try {
      $start_date_obj = new DrupalDateTime($start_date_iso);
      $end_date_obj = new DrupalDateTime($end_date_iso);
    }
    catch (\Exception $e) {
      // Handle invalid date strings.
      $variables['content']['event_date'] = ['#markup' => 'Invalid Date'];
      \Drupal::logger('bwy_theme')
        ->error(
          'Failed to parse date range for node @nid: @message',
          ['@nid' => $node->id(), '@message' => $e->getMessage()]
        );
      return;
    }

    // Initialize the string.
    $formatted_date_string = '';
    $date_formatter = \Drupal::service('date.formatter');

    if ($show_month_only) {
      // If the checkbox is checked, display only the month of the start date.
      $formatted_date_string = $date_formatter->format($start_date_obj->getTimestamp(), 'bwy_event_date_month_only');
    }
    else {
      $start_day = $start_date_obj->format('j');
      $end_day = $end_date_obj->format('j');
      $start_month = $start_date_obj->format('n');
      $end_month = $end_date_obj->format('n');
      $start_year = $start_date_obj->format('Y');
      $end_year = $end_date_obj->format('Y');

      if ($start_date_obj->format('Y-m-d') === $end_date_obj->format('Y-m-d')) {
        // Case 1: Start date and end date are on the exact same day.
        $formatted_date_string = $date_formatter->format($start_date_obj->getTimestamp(), 'bwy_event_date');
      }
      elseif ($start_year === $end_year && $start_month === $end_month) {
        // Case 2: Different days, but within the same month and year.
        $formatted_date_string = $start_day . '-' . $end_day . ' ' . $date_formatter->format($start_date_obj->getTimestamp(), 'custom', 'F Y');
      }
      elseif ($start_year === $end_year) {
        // Case 3: Different months, but within the same year.
        $formatted_start = $date_formatter->format($start_date_obj->getTimestamp(), 'custom', 'j F');
        $formatted_end = $date_formatter->format($end_date_obj->getTimestamp(), 'custom', 'j F Y');
        $formatted_date_string = $formatted_start . ' - ' . $formatted_end;
      }
      else {
        // Case 4: Dates span across different years.
        $formatted_start = $date_formatter->format($start_date_obj->getTimestamp(), 'custom', 'j F Y');
        $formatted_end = $date_formatter->format($end_date_obj->getTimestamp(), 'custom', 'j F Y');
        $formatted_date_string = $formatted_start . ' - ' . $formatted_end;
      }
    }

    // Assign the formatted string wrapped in a #markup to make it renderable.
    $variables['content']['event_date'] = ['#markup' => $formatted_date_string];
  }
  else {
    // If the fields are missing or empty, ensure an empty string.
    $variables['content']['event_date'] = ['#markup' => ''];
  }

  if (in_array($variables['view_mode'], ['teaser', 'featured', 'full']) &&
      !empty($variables['content']['field_teaser_image'][0])) {
    $variables['content']['field_teaser_image'][0]['#item_attributes']['class'][] = 'w-full';
    $variables['content']['field_teaser_image'][0]['#item_attributes']['class'][] = 'h-full';
    $variables['content']['field_teaser_image'][0]['#item_attributes']['class'][] = 'object-cover';
  }

  // Get the referenced speaker entities.
  $variables['content']['speakers'] = [];
  if ($node->hasField('field_schedule') && !$node->get('field_schedule')->isEmpty()) {

    // Load the view and set the display.
    $view = Views::getView('speakers');
    if ($view && $view->access('default')) {

      $view->setDisplay('default');
      $view->preExecute();
      $view->execute();

      // Check if there are results.
      if (!empty($view->result)) {

        // Render the view.
        $rendered_view = $view->render();
        $variables['content']['speakers'] = $rendered_view;
      }
      else {

        // No results found.
        $variables['content']['speakers'] = NULL;
      }
    }
    else {

      // View does not exist or access denied.
      $variables['content']['speakers'] = NULL;
    }
  }
}

/**
 * Implements hook_preprocess_node().
 */
function bwy_preprocess_node(&$variables) {
  /** @var \Drupal\node\Entity\Node $node */
  $node = $variables['node'];
  $bundle = $node->bundle();
  $view_mode = $variables['view_mode'];

  switch ($bundle) {
    case 'landing_page':
      if ($view_mode === 'full') {
        // Get the hero theme value, default to 'default' if not set.
        $hero_theme_variant = 'default';
        $display_hero_section = FALSE;

        // Get the hero theme.
        if ($node->hasField('field_hero_theme') && !$node->get('field_hero_theme')->isEmpty()) {
          $hero_theme_variant = $node->get('field_hero_theme')->first()->getString();
          $display_hero_section = TRUE;
        }

        // Get subtitle if available.
        $hero_subtitle = '';
        if ($node->hasField('field_hero_subtitle') && !$node->get('field_hero_subtitle')->isEmpty()) {
          $hero_subtitle = $node->get('field_hero_subtitle')->value;
        }

        // Get button link and text if available.
        $hero_button_url = '';
        $hero_button_text = '';
        if ($node->hasField('field_hero_button') && !$node->get('field_hero_button')->isEmpty()) {
          $link_item = $node->get('field_hero_button')->first();
          if ($link_item) {
            $hero_button_url = $link_item->getUrl()->toString();
            $hero_button_text = $link_item->title;
          }
        }

        // Add to template variables.
        $variables['hero_theme_variant'] = $hero_theme_variant;
        $variables['display_hero_section'] = $display_hero_section;
        $variables['hero_subtitle'] = $hero_subtitle;
        $variables['hero_button_url'] = $hero_button_url;
        $variables['hero_button_text'] = $hero_button_text;
      }
      break;

    case 'job_post':
      // Process salary information for job_post content type.
      if (
        $node->hasField('field_salary_from') &&
        $node->hasField('field_salary_to') &&
        $node->hasField('field_gross_net')
      ) {
        $variables['salary_range'] = _bwy_format_salary_range(
          $node,
          'field_salary_from',
          'field_salary_to',
          $node->get('field_gross_net')->value
        );
      }
      break;

    case 'event':
      /** @var \Drupal\address\Entity\Address $address */
      $address = $node->get('field_location')->first();
      if ($address) {
        $country_code = $address->getCountryCode();
        $langcode = \Drupal::languageManager()->getCurrentLanguage(LanguageInterface::TYPE_INTERFACE)->getId();
        $country = \Drupal::service('address.country_repository')->get($country_code, $langcode);
        $country_name = $country ? $country->getName() : '';

        $locality = $address->getLocality() ?? '';
        $location_string = $locality && $country_name
          ? $locality . ', ' . $country_name
          : ($locality ?: ($country_name ?: ''));

        $variables['content']['location_string'] = ['#markup' => $location_string];
      }
      break;
  }

  // Handle hero theme variants for content types.
  if ($view_mode === 'full') {
    $theme_variants = [
      'job_post' => 'yellow',
      'news' => 'pink',
      'event' => 'red',
    ];
    if (isset($theme_variants[$bundle])) {
      $variables['hero_theme_variant'] = $theme_variants[$bundle];
      $variables['display_hero_section'] = TRUE;
    }
  }
}
