{#
/**
 * @file
 * Company Card component.
 */
#}

{% set classes = [
  'company-card',
  'h-full',
  'bg-white',
  'border',
  'border-solid',
  'border-border-main',
  'rounded-lg',
  'relative',
  'flex',
  'flex-col',
  'justify-between',
  'items-center',
  'gap-4',
  'body-1',
  'text-text-main',
] %}

{# Initialize attributes if not provided #}
{% if company_card_attributes is not defined %}
  {% set company_card_attributes = create_attribute() %}
{% endif %}

<div {{company_card_attributes.addClass(classes)}}>
  {# Company logo, name and Number of job offers #}
  <div class="p-4 flex flex-col items-center gap-4">
    {% if company_logo is not empty %}
      {# TO DO: Remove the height and width when we apply the image styles. #}
      <div class="company-logo w-24 h-24 flex items-center justify-center">
        {{ company_logo }}
      </div>
    {% endif %}

    {% if label is not empty or count is not empty %}
      <div class="flex flex-col gap-1 items-center">
        {% if label is not empty %}
          <div class="heading-3-mobile text-center">
            {{ label }}
          </div>
        {% endif %}
        {% if count is not empty %}
          <div class="text-blue-600">
            {{ count }}
          </div>
        {% endif %}
      </div>
    {% endif %}
  </div>

  {% if view_group is not empty %}
    <div class="w-full text-center p-4 border-t border-solid border-border-main">
      {% include '@bwy/components/button/button.twig' with {
        text: read_more_text|default('Details'|t),
        button_type: 'link',
        tag: 'a',
        with_icon: true,
      } %}
    </div>

    {% include '@bwy/components/accessible_link/accessible_link.twig' with {
      url: view_group,
      title: label
    } %}
  {% endif %}
</div>
