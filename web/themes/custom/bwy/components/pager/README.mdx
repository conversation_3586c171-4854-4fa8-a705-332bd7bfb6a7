# Pager Component

A pagination component that provides navigation through multiple pages of content. The component includes previous/next arrows and page numbers, with ellipses for large page ranges.

## Features

- Previous/Next navigation arrows
- Current page indicator
- Page number links
- Ellipses for large page ranges
- Responsive design (mobile and desktop sizes)
- Accessible navigation with ARIA labels
- Visual feedback on hover states

## Usage

```twig
{% include '@bwy/components/pager/pager.twig' with {
  heading_id: 'pagination-heading',
  items: {
    previous: {
      href: '/page/1'
    },
    next: {
      href: '/page/3'
    },
    pages: {
      1: { href: '/page/1' },
      2: { href: '/page/2' },
      3: { href: '/page/3' }
    }
  },
  current: 2,
  ellipses: {
    previous: false,
    next: true
  }
} only %}
```

## Props

- `heading_id` (string, required): Unique ID for the pagination heading
- `items` (object, required): Contains navigation items
  - `previous` (object): Previous page link information
  - `next` (object): Next page link information
  - `pages` (object): List of page numbers and their links
- `current` (number, required): Current page number
- `ellipses` (object): Controls ellipsis display
  - `previous` (boolean): Show ellipsis before visible pages
  - `next` (boolean): Show ellipsis after visible pages

## Styling

The component uses Tailwind CSS utility classes for styling:
- Responsive sizing (w-7/h-7 on mobile, w-10/h-10 on desktop)
- Hover states (hover:text-blue-600)
- Current page indicator (bg-blue-600, text-white, rounded-full)
- Disabled state (text-gray-300)
- Flex layout for alignment and spacing

## Dependencies

- Icon with Text component (@bwy/components/icon_with_text/icon_with_text.twig)
- Chevron icons (chevron_left_filled, chevron_right_filled) 