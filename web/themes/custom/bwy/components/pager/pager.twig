{#
/**
 * @file
 * Pager component.
 */
#}

{% if items %}
  <nav class="pager mt-10" role="navigation" aria-labelledby="{{ heading_id }}">
    <div id="{{ heading_id }}" class="visually-hidden">{{ 'Pagination'|t }}</div>
    <ul class="pager__items js-pager__items flex justify-center items-center gap-1.5">

      {# Previous arrow #}
      <li>
        {% if items.previous %}
          <a href="{{ items.previous.href }}"
             title="{{ 'Go to previous page'|t }}"
             rel="prev"
             class="text-black hover:text-blue-600 text-xl w-7 h-7 md:w-10 md:h-10 flex items-center justify-center">
            {% include '@bwy/components/icon_with_text/icon_with_text.twig' with {
              icon_name: 'chevron_left_filled',
              text: 'Previous page'|t,
              text_classes: 'visually-hidden'
            } only %}
          </a>
        {% else %}
          <span class="text-gray-300 text-xl w-7 h-7 md:w-10 md:h-10 flex items-center justify-center select-none">
            {% include '@bwy/components/icon_with_text/icon_with_text.twig' with {
              icon_name: 'chevron_left_filled',
              text: '',
              icon_classes: 'text-gray-300 text-xl w-7 h-7 md:w-10 md:h-10 flex items-center justify-center select-none'
            } only %}
          </span>
        {% endif %}
      </li>

      {# Ellipsis before visible pages #}
      {% if ellipses.previous %}
        <li class="text-gray-400 w-7 h-7 md:w-10 md:h-10 flex items-center justify-center select-none">…</li>
      {% endif %}

      {# Page numbers #}
      {% for key, item in items.pages %}
        <li>
          {% if current == key %}
            <span class="bg-blue-600 text-white font-medium rounded-full w-7 h-7 md:w-10 md:h-10 flex items-center justify-center">
              {{ key }}
            </span>
          {% else %}
            <a href="{{ item.href }}"
               title="{{ 'Go to page @key'|t({'@key': key}) }}"
               class="text-black hover:text-blue-600 w-7 h-7 md:w-10 md:h-10 flex items-center justify-center">
              {{ key }}
            </a>
          {% endif %}
        </li>
      {% endfor %}

      {# Ellipsis after visible pages #}
      {% if ellipses.next %}
        <li class="text-gray-400 w-7 h-7 md:w-10 md:h-10 flex items-center justify-center select-none">…</li>
      {% endif %}

      {# Next arrow #}
      <li>
        {% if items.next %}
          <a href="{{ items.next.href }}"
             title="{{ 'Go to next page'|t }}"
             rel="next"
             class="text-black hover:text-blue-600 text-xl w-7 h-7 md:w-10 md:h-10 flex items-center justify-center">
            {% include '@bwy/components/icon_with_text/icon_with_text.twig' with {
              icon_name: 'chevron_right_filled',
              text: 'Next page'|t,
              text_classes: 'visually-hidden'
            } only %}
          </a>
        {% else %}
          <span class="text-gray-300 text-xl w-7 h-7 md:w-10 md:h-10 flex items-center justify-center select-none">
            {% include '@bwy/components/icon_with_text/icon_with_text.twig' with {
              icon_name: 'chevron_right_filled',
              text: '',
              icon_classes: 'text-gray-300 text-xl w-7 h-7 md:w-10 md:h-10 flex items-center justify-center select-none'
            } only %}
          </span>
        {% endif %}
      </li>

    </ul>
  </nav>
{% endif %} 