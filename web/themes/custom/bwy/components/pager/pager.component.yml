name: Pager
description: A pagination component for navigating through multiple pages of content.
props:
  heading_id:
    type: string
    description: The ID for the pagination heading.
    required: true
  items:
    type: array
    description: List of pager items including previous, next, and page numbers.
    items:
      type: object
      properties:
        href:
          type: string
          description: URL for the page link
        text:
          type: string
          description: Text to display for the link
  current:
    type: integer
    description: The current page number.
    required: true
  ellipses:
    type: array
    description: Indicates if ellipses should be shown before or after page numbers.
    items:
      type: boolean
      description: Show ellipsis
dependencies:
  - "@bwy/components/icon_with_text/icon_with_text.twig" 