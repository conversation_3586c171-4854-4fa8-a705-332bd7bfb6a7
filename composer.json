{"name": "drupal/recommended-project", "description": "Project template for Drupal projects with a relocated document root", "type": "project", "license": "GPL-2.0-or-later", "homepage": "https://www.drupal.org/project/drupal", "support": {"docs": "https://www.drupal.org/docs/user_guide/en/index.html", "chat": "https://www.drupal.org/node/314178"}, "repositories": [{"type": "composer", "url": "https://packages.drupal.org/8", "canonical": false}, {"type": "vcs", "url": "https://git.drupalcode.org/issue/entitygroupfield-3515583.git"}, {"type": "package", "package": {"name": "colorbox/colorbox", "version": "1.6.4", "type": "drupal-library", "dist": {"url": "https://github.com/jackmoore/colorbox/archive/refs/tags/1.6.4.zip", "type": "zip"}}}, {"type": "package", "package": {"name": "toc/toc", "version": "v0.3.2", "type": "drupal-library", "dist": {"url": "https://raw.githubusercontent.com/jgallen23/toc/greenkeeper/update-all/dist/toc.min.js", "type": "file"}}}, {"type": "package", "package": {"name": "noli42/chosen", "version": "3.0.0", "type": "drupal-library", "dist": {"url": "https://github.com/noli42/chosen/releases/download/3.0.0/chosen-assets-v3.0.0.zip", "type": "zip"}}}], "require": {"colorbox/colorbox": "^1.6", "composer/installers": "^2.3", "cweagans/composer-patches": "^1.7", "drupal/address": "^2.0", "drupal/addtoany": "^2.0", "drupal/better_exposed_filters": "^7.0a", "drupal/choices_autocomplete": "^1.3", "drupal/chosen": "^5.0", "drupal/colorbox": "^2.1", "drupal/conditional_fields": "^4.0@alpha", "drupal/config_ignore": "^3.3", "drupal/core-composer-scaffold": "^11.1", "drupal/core-project-message": "^11.1", "drupal/core-recommended": "^11.1", "drupal/cshs": "^4.0", "drupal/csp": "^2.2", "drupal/entitygroupfield": "dev-3515583-drupal-11-compatibility", "drupal/environment_indicator": "^4.0", "drupal/facets": "^3.0", "drupal/field_group": "^4.0", "drupal/gin": "^4.0", "drupal/gin_login": "^2.1", "drupal/group": "^3.3", "drupal/inline_entity_form": "^3.0@RC", "drupal/login_history": "^2.0", "drupal/masquerade": "^2.0", "drupal/menu_link_attributes": "^1.5", "drupal/metatag": "^2.1", "drupal/node_read_time": "^1.15", "drupal/node_weight": "^3.0", "drupal/paragraphs": "^1.19", "drupal/pathauto": "^1.13", "drupal/phone_number": "^2.0@alpha", "drupal/private_message": "^3.0", "drupal/profile": "^1.12", "drupal/rabbit_hole": "^2.0@beta", "drupal/range": "^1.6", "drupal/redirect": "^1.11", "drupal/redis": "^1.9", "drupal/registration_role": "^2.0", "drupal/s3fs": "^3.8", "drupal/scheduler": "^2.2", "drupal/scheduler_content_moderation_integration": "^3.0", "drupal/search_api": "^1.38", "drupal/search_api_attachments": "^10.0", "drupal/search_api_solr": "^4.3", "drupal/seckit": "^2.0", "drupal/simple_sitemap": "^4.2", "drupal/simple_time_field": "^1.2", "drupal/social_auth": "^4.1", "drupal/social_auth_facebook": "^4.0", "drupal/social_auth_google": "^4.0", "drupal/structure_sync": "^2.0", "drupal/symfony_mailer": "^1.5", "drupal/taxonomy_machine_name": "^2.0", "drupal/toc_filter": "^2.3", "drupal/toc_js": "^3.1", "drupal/twig_tweak": "^3.4", "drupal/ui_patterns": "^2.0", "drupal/views_kanban": "^1.0", "drupal/views_load_more": "^2.0", "drupal/viewsreference": "^2.0@beta", "drupal/webform": "^6.3@beta", "drupal/webform_content_creator": "^4.0", "drush/drush": "^13.5", "noli42/chosen": "^3.0.0", "oomphinc/composer-installers-extender": "^2.0", "toc/toc": "^0.3.2", "vlucas/phpdotenv": "^5.6"}, "conflict": {"drupal/drupal": "*"}, "minimum-stability": "stable", "prefer-stable": true, "config": {"allow-plugins": {"composer/installers": true, "cweagans/composer-patches": true, "dealerdirect/phpcodesniffer-composer-installer": true, "drupal/core-composer-scaffold": true, "drupal/core-project-message": true, "oomphinc/composer-installers-extender": true, "php-http/discovery": true, "phpstan/extension-installer": true, "tbachert/spi": true}, "sort-packages": true}, "autoload": {"classmap": ["scripts/composer/ScriptHandler.php", "scripts/composer/BulcodeSetupCodeAndCommitValidatorsHandler.php", "scripts/composer/BulcodeUpdateDrupalHandler.php"], "files": ["load.environment.php"]}, "scripts": {"pre-install-cmd": ["DrupalProject\\composer\\ScriptHandler::checkComposerVersion"], "pre-update-cmd": ["DrupalProject\\composer\\ScriptHandler::checkComposerVersion", "DrupalComposerManaged\\ComposerScripts::preUpdate"], "post-install-cmd": ["DrupalProject\\composer\\ScriptHandler::createRequiredFiles", "DrupalProject\\composer\\BulcodeSetupCodeAndCommitValidatorsHandler::doSetup"], "post-update-cmd": ["DrupalProject\\composer\\ScriptHandler::createRequiredFiles", "DrupalComposerManaged\\ComposerScripts::postUpdate"], "update-drupal": "DrupalProject\\composer\\BulcodeUpdateDrupalHandler::updateDrupal"}, "extra": {"composer-exit-on-patch-failure": true, "patchLevel": {"drupal/core": "-p2"}, "drupal-scaffold": {"locations": {"web-root": "web/"}}, "installer-paths": {"web/core": ["type:drupal-core"], "web/libraries/{$name}": ["type:drupal-library"], "web/modules/contrib/{$name}": ["type:drupal-module"], "web/profiles/contrib/{$name}": ["type:drupal-profile"], "web/themes/contrib/{$name}": ["type:drupal-theme"], "drush/Commands/contrib/{$name}": ["type:drupal-drush"], "web/modules/custom/{$name}": ["type:drupal-custom-module"], "web/profiles/custom/{$name}": ["type:drupal-custom-profile"], "web/themes/custom/{$name}": ["type:drupal-custom-theme"], "recipes/{$name}": ["type:drupal-recipe"]}, "patches": {"drupal/group": {"[#3512941] Allow load group members with custom fields conditions": "https://www.drupal.org/files/issues/2025-03-14/3512941-3.3.3-2.patch", "[#3378653] Group content view errors": "https://www.drupal.org/files/issues/2023-10-20/3378653-5-group-content-view-errors.patch"}, "drupal/core": {"[#3301782] PHP8 Deprecated function : htmlspecialchars(): Passing null to parameter #1 ($string) of type string is deprecated": "https://www.drupal.org/files/issues/2025-02-13/contact-message-subject-or-sender-null-3301782-32_0.patch", "[#3523317] TypeError: Drupal\\Component\\Utility\\Html::escape(): Argument #1 ($text) must be of type string, null given": "https://www.drupal.org/files/issues/2025-05-08/handle-null-in-render-html.patch", "[#3052608] DateTimeComputed tries to compute from an empty value": "https://git.drupalcode.org/project/drupal/-/merge_requests/7585.patch", "[#3204558] Timestamp field should use date filter plugin rather than numeric filter plugin": "https://git.drupalcode.org/project/drupal/-/merge_requests/10254.patch", "CSS class validation too strict": "patches/views-css-classes-11-1-7.patch"}, "drupal/rabbit_hole": {"[#3520603] Enabling new entity types on form throws error": "https://git.drupalcode.org/project/rabbit_hole/-/merge_requests/84.patch"}, "drupal/toc_filter": {"[#3518549] Make TocFilterBlock::blockAccess() return type be compatible with parent": "https://www.drupal.org/files/issues/2025-04-24/toc-filter-3518549-13.patch"}, "drupal/views_load_more": {"[#3496870] Issue with Ajax functionality and Drupal 11": "https://www.drupal.org/files/issues/2025-01-25/issue-with-ajax-functionality-and-drupal-11-3496870-3.patch"}}, "drupal-core-project-message": {"include-keys": ["homepage", "support"], "post-create-project-cmd-message": ["<bg=blue;fg=white>                                                         </>", "<bg=blue;fg=white>  Congratulations, you’ve installed the Drupal codebase  </>", "<bg=blue;fg=white>  from the drupal/recommended-project template!          </>", "<bg=blue;fg=white>                                                         </>", "", "<bg=yellow;fg=black>Next steps</>:", "  * Install the site: https://www.drupal.org/docs/installing-drupal", "  * Read the user guide: https://www.drupal.org/docs/user_guide/en/index.html", "  * Get support: https://www.drupal.org/support", "  * Get involved with the Drupal community:", "      https://www.drupal.org/getting-involved", "  * Remove the plugin that prints this message:", "      composer remove drupal/core-project-message"]}}, "require-dev": {"drupal/coder": "^8.3", "drupal/core-dev": "^11.1", "squizlabs/php_codesniffer": "^3.13"}}